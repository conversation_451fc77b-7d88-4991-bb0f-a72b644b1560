import { ConfigEnv, UserConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import { loadEnv } from "vite";

// https://vite.dev/config/
export default ({ mode }: ConfigEnv): UserConfig => {
  const env = loadEnv(mode, path.resolve(process.cwd()));
  return {
    plugins: [react()],
    base: "./",

    resolve: {
      alias: [
        { find: /^~/, replacement: "" },
        { find: /^~/, replacement: path.resolve(__dirname, "./") },
        { find: "@", replacement: path.resolve(__dirname, "src") },
        { find: "@c", replacement: path.resolve(__dirname, "config") },
      ],
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          modifyVars: {},
        },
      },
    },
    server: {
      host: true,
      port: 1315,
      proxy: {
        "/server": {
          target: env.VITE_BASE_API,
          changeOrigin: true,
          rewrite: (pre) => pre.replace(/^\/server/, ""),
        },
      },
    },
  };
};
