{"name": "security-examination-console-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite --mode dev", "dev": "vite --mode dev", "build": "tsc -b && vite build --mode dev", "build:prod": "tsc -b && vite build --mode prod", "lint": "eslint .", "preview": "vite preview", "prettier": "prettier . --write"}, "dependencies": {"ahooks": "^3.8.4", "antd": "^5.24.6", "antd-img-crop": "^4.25.0", "axios": "^1.8.4", "classnames": "^2.5.1", "dayjs": "^1.11.13", "echarts": "^5.6.0", "jszip": "^3.10.1", "react": "18.3.1", "react-dom": "18.3.1", "react-quill": "^2.0.0", "react-router-dom": "^7.5.0", "react-to-print": "^3.0.6", "xlsx": "^0.18.5"}, "devDependencies": {"@ant-design/colors": "^8.0.0", "@ant-design/cssinjs": "^1.23.0", "@eslint/js": "^9.21.0", "@types/node": "^22.14.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "less": "^4.3.0", "prettier": "3.5.3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}