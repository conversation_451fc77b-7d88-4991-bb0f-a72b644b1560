import { Outlet } from "react-router";
import Home from "@/pages/Home";
import Layout from "@/Layout";
import Login from "@/pages/Login";
import Qualification from "@/pages/Qualification";
import Examination from "@/pages/Examination";
import ExaminationDetail from "@/pages/ExaminationDetail";
import Admission from "@/pages/Admission";
import Score from "@/pages/Score";
import Certificate from "@/pages/Certificate";
import Staff from "@/pages/Staff";
import Registration from "@/pages/Registration";
import AdmissionDetail from "@/pages/AdmissionDetail";
import ScoreDetail from "@/pages/ScoreDetail";
import CertificateDetail from "@/pages/CertificateDetail";
import Register from "@/pages/Register";
import SetupApply from "@/pages/Enterprise/SetupApply";
import SetupApproval from "@/pages/Enterprise/SetupApproval";
import SetupApprovalDetail from "@/pages/Enterprise/SetupApprovalDetail";
import SetupEnterpriseDetail from "@/pages/Enterprise/SetupEnterpriseDetail";
import ChangeApply from "@/pages/Enterprise/ChangeApply";
import ChangeApprovalDetail from "@/pages/Enterprise/ChangeApprovalDetail";
import ChangeApproval from "@/pages/Enterprise/ChangeApproval";
import RequireMaterials from "@/pages/Enterprise/RequireMaterials";
import ApprovalFlow from "@/pages/Enterprise/ApprovalFlow";
import ChangeEnterpriseDetail from "@/pages/Enterprise/ChangeEnterpriseDetail";
import EnterpriseAccountSummary from "@/pages/General/EnterpriseAccount/EnterpriseAccountSummary";
import EnterpriseAccountList from "@/pages/General/EnterpriseAccount/EnterpriseAccountList";
import EnterpriseCustomerSummary from "@/pages/General/EnterpriseCustomer/EnterpriseCustomerSummary";
import EnterpriseCustomerList from "@/pages/General/EnterpriseCustomer/EnterpriseCustomerList";
import SecurityGuardSummary from "@/pages/General/SecurityGuard/SecurityGuardSummary";
import SecurityGuardType from "@/pages/General/SecurityGuard/SecurityGuardType";
import SecurityGuardList from "@/pages/General/SecurityGuard/SecurityGuardList";
import EnterpriseLicenseSummary from "@/pages/License/License/EnterpriseLicenseSummary";
import EnterpriseLicenseList from "@/pages/License/License/EnterpriseLicenseList";
import EnterpriseAnnualReviewPage from "@/pages/License/annual/EnterpriseAnnualReviewPage";
import EnterpriseAnnualReviewList from "@/pages/License/annual/EnterpriseAnnualReviewList";
import Send from "@/pages/Message/Send";
import Reply from "@/pages/Message/Reply";
import Publish from "@/pages/Notification/Publish";
import View from "@/pages/Notification/View";
import Training from "@/pages/Training";
import {
  HomeOutlined,
  FormOutlined,
  FileSearchOutlined,
  EditOutlined,
  IdcardOutlined,
  FileDoneOutlined,
  TeamOutlined,
  BarChartOutlined,
  MailOutlined,
  MessageOutlined,
  RetweetOutlined,
  UserOutlined,
  FileTextOutlined,
  BellOutlined,
  SendOutlined,
  EyeOutlined,
  FileAddOutlined,
  SwapOutlined,
  AuditOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";

const unauthRoutes = [
  {
    path: "/",
    element: <Layout></Layout>,
    types: [0, 1],
    roles: [1, 2],
    children: [],
  },
  {
    path: "/login",
    types: [0, 1],
    roles: [1, 2],
    element: <Login></Login>,
  },
  {
    path: "/register",
    types: [1],
    element: <Register></Register>,
  },
];

const authRoutes = [
  {
    path: "/",
    element: <Layout></Layout>,
    types: [0, 1],
    roles: [1, 2],
    children: [
      {
        path: "/home",
        element: <Home />,
        name: "首页",
        types: [0, 1],
        roles: [1, 2],
        icon: <HomeOutlined />,
      },
      {
        path: "/exam",
        element: <Outlet />,
        name: "保安考试管理系统",
        types: [0, 1],
        roles: [1, 2],
        icon: <TeamOutlined />,
        children: [
          {
            path: "/exam/registration",
            element: <Registration />,
            name: "报名考试管理",
            types: [0, 1],
            roles: [1, 2],
            icon: <FormOutlined />,
          },
          {
            path: "/exam/qualification",
            element: <Qualification />,
            name: "资格审查管理",
            types: [0],
            roles: [1, 2],
            icon: <FileSearchOutlined />,
          },
          {
            path: "/exam/examination",
            element: <Examination />,
            name: "考试管理",
            types: [0, 1],
            roles: [1],
            icon: <FileDoneOutlined />,
          },
          {
            path: "/exam/examination/:examUserScheduleId",
            element: <ExaminationDetail />,
            name: "考试详情",
            types: [0, 1],
            roles: [1],
            hidden: true,
            icon: <FileDoneOutlined />,
          },
          {
            path: "/exam/admission",
            element: <Admission />,
            name: "准考证管理",
            types: [0, 1],
            roles: [1],
            icon: <FileTextOutlined />,
          },
          {
            path: "/exam/admission/:examUserScheduleId",
            element: <AdmissionDetail />,
            name: "准考证详情",
            types: [0, 1],
            roles: [1],
            hidden: true,
            icon: <EditOutlined />,
          },
          {
            path: "/exam/score",
            element: <Score />,
            name: "成绩管理",
            types: [0, 1],
            roles: [1],
            icon: <BarChartOutlined />,
          },
          {
            path: "/exam/score/:examUserScheduleId",
            element: <ScoreDetail />,
            name: "成绩详情",
            hidden: true,
            types: [0, 1],
            roles: [1],
            icon: <BarChartOutlined />,
          },
          {
            path: "/exam/certificate",
            element: <Certificate />,
            name: "保安员证管理",
            types: [0, 1],
            roles: [1],
            icon: <IdcardOutlined />,
          },
          {
            path: "/exam/certificate/:examUserScheduleId",
            element: <CertificateDetail />,
            name: "证书详情",
            hidden: true,
            types: [0, 1],
            roles: [1],
            icon: <IdcardOutlined />,
          },
          {
            path: "/exam/training",
            element: <Training />,
            name: "模拟考试管理",
            types: [0, 1],
            roles: [1],
            icon: <IdcardOutlined />,
          },
        ],
      },
      {
        path: "/enterprise",
        element: <Outlet />,
        name: "许可证审核审批管理",
        types: [0, 1],
        roles: [1],
        icon: <FileDoneOutlined />,
        children: [
          {
            path: "/enterprise/setupApply",
            element: <SetupApply />,
            name: "企业设立申请",
            types: [1],
            roles: [],
            icon: <FileAddOutlined />,
          },
          {
            path: "/enterprise/setupApply/detail/:id",
            element: <SetupApprovalDetail />,
            name: "审查详情",
            types: [1],
            roles: [],
            hidden: true,
          },
          {
            path: "/enterprise/changeApply",
            element: <ChangeApply />,
            name: "企业信息变更申请",
            types: [1],
            roles: [],
            icon: <SwapOutlined />,
          },
          {
            path: "/enterprise/changeApply/detail/:id",
            element: <ChangeApprovalDetail />,
            name: "审查详情",
            types: [1],
            roles: [],
            hidden: true,
          },
          {
            path: "/enterprise/setupApproval",
            element: <SetupApproval />,
            name: "企业设立审核",
            types: [0],
            roles: [1],
            icon: <AuditOutlined />,
          },
          {
            path: "/enterprise/setupApproval/enterpriseDetail/:id",
            element: <SetupEnterpriseDetail />,
            name: "公司详情",
            types: [0],
            roles: [1],
            hidden: true,
          },
          {
            path: "/enterprise/setupApproval/detail/:id",
            element: <SetupApprovalDetail />,
            name: "审查详情",
            types: [0],
            roles: [1],
            hidden: true,
          },
          {
            path: "/enterprise/setupApproval/requireMaterials",
            element: <RequireMaterials />,
            name: "审批资料",
            types: [0],
            roles: [1],
            hidden: true,
          },
          {
            path: "/enterprise/setupApproval/approvalFlow",
            element: <ApprovalFlow />,
            name: "审批流程",
            types: [0],
            roles: [1],
            hidden: true,
          },
          {
            path: "/enterprise/changeApproval",
            element: <ChangeApproval />,
            name: "企业信息变更审核",
            types: [0],
            roles: [1],
            icon: <CheckCircleOutlined />,
          },
          {
            path: "/enterprise/changeApproval/enterpriseDetail/:id",
            element: <ChangeEnterpriseDetail />,
            name: "公司详情",
            types: [0],
            roles: [1],
            hidden: true,
          },
          {
            path: "/enterprise/changeApproval/detail/:id",
            element: <ChangeApprovalDetail />,
            name: "审查详情",
            types: [0],
            roles: [1],
            hidden: true,
          },
          {
            path: "/enterprise/changeApproval/requireMaterials",
            element: <RequireMaterials />,
            name: "审批资料",
            types: [0],
            roles: [1],
            hidden: true,
          },
        ],
      },
      {
        path: "/license",
        element: <Outlet />,
        name: "服务许可证管理",
        types: [0],
        roles: [1],
        icon: <FileDoneOutlined />,
        children: [
          {
            path: "/license/license",
            element: <EnterpriseLicenseSummary />,
            name: "许可证管理",
            types: [0],
            roles: [1],
            icon: <FileAddOutlined />,
          },
          {
            path: "/license/license/:serviceType",
            element: <EnterpriseLicenseList />,
            name: "许可证详情",
            types: [0],
            roles: [1],
            hidden: true,
          },
          {
            path: "/license/annual",
            element: <EnterpriseAnnualReviewPage />,
            name: "年检管理",
            types: [0],
            roles: [1],
            icon: <FileAddOutlined />,
          },
          {
            path: "/license/annual/:id",
            element: <EnterpriseAnnualReviewList />,
            name: "年检记录",
            types: [0],
            roles: [1],
            hidden: true,
          },
        ],
      },
      {
        path: "/general",
        element: <Outlet />,
        name: "综合信息管理",
        types: [0, 1],
        roles: [1],
        icon: <FileDoneOutlined />,
        children: [
          {
            path: "/general/account",
            element: <EnterpriseAccountSummary />,
            name: "企业人员管理",
            types: [0, 1],
            roles: [1],
            icon: <FileAddOutlined />,
          },
          {
            path: "/general/account/:serviceType",
            element: <EnterpriseAccountList />,
            name: "企业人员查询",
            types: [0, 1],
            roles: [1],
            hidden: true,
          },
          {
            path: "/general/customer",
            element: <EnterpriseCustomerSummary />,
            name: "客户信息管理",
            types: [0, 1],
            roles: [1],
            icon: <FileAddOutlined />,
          },
          {
            path: "/general/customer/:enterpriseId",
            element: <EnterpriseCustomerList />,
            name: "客户列表",
            types: [0, 1],
            roles: [1],
            hidden: true,
          },
          {
            path: "/general/securityGuard",
            element: <SecurityGuardSummary />,
            name: "保安员信息管理",
            types: [0, 1],
            roles: [1],
            icon: <FileAddOutlined />,
          },
          {
            path: "/general/securityGuard/type",
            element: <SecurityGuardType />,
            name: "保安员分类列表",
            types: [0, 1],
            roles: [1],
            hidden: true,
          },
          {
            path: "/general/securityGuard/type/all",
            element: <SecurityGuardList />,
            name: "人员信息查询",
            types: [0, 1],
            roles: [1],
            hidden: true,
          },
          {
            path: "/general/securityGuard/type/0",
            element: <SecurityGuardList />,
            name: "考试获证人员管理",
            types: [0, 1],
            roles: [1],
            hidden: true,
          },
          {
            path: "/general/securityGuard/type/1",
            element: <SecurityGuardList />,
            name: "持证人员管理",
            types: [0, 1],
            roles: [1],
            hidden: true,
          },
          {
            path: "/general/securityGuard/type/2",
            element: <SecurityGuardList />,
            name: "离职人员管理",
            types: [0, 1],
            roles: [1],
            hidden: true,
          },
        ],
      },
      {
        path: "/message",
        element: <Outlet />,
        name: "留言管理",
        types: [0, 1],
        roles: [1],
        icon: <MessageOutlined />,
        children: [
          {
            path: "/message/send",
            element: <Send />,
            name: "发送管理",
            types: [1],
            roles: [],
            icon: <MailOutlined />,
          },
          {
            path: "/message/reply",
            element: <Reply />,
            name: "回复管理",
            types: [0],
            roles: [1],
            icon: <RetweetOutlined />,
          },
        ],
      },
      {
        path: "/notification",
        element: <Outlet />,
        name: "通知通告管理",
        types: [0, 1],
        roles: [1],
        icon: <BellOutlined />,
        children: [
          {
            path: "/notification/publish",
            element: <Publish />,
            name: "发布管理",
            types: [0],
            roles: [1],
            icon: <SendOutlined />,
          },
          {
            path: "/notification/view",
            element: <View />,
            name: "查看管理",
            types: [1],
            roles: [],
            icon: <EyeOutlined />,
          },
        ],
      },
      {
        path: "/staff",
        element: <Staff />,
        name: "管理人员",
        types: [0],
        roles: [1, 2],
        icon: <UserOutlined />,
      },
    ],
  },
  {
    path: "/login",
    types: [0, 1],
    roles: [1, 2],
    element: <Login></Login>,
  },
  {
    path: "/register",
    types: [1],
    element: <Register></Register>,
  },
];

// 0 - 后台用户  1 - 企业用户
export const getRoutes = (routes, type: number, role: number) => {
  if (type === undefined) {
    return unauthRoutes;
  }

  return routes.reduce((memo, route) => {
    if (type === 0) {
      if (route.types?.includes(type) && route.roles?.includes(role)) {
        if (route.children) {
          route.children = getRoutes(route.children, type, role);
        }
        return [...memo, route];
      } else {
        return memo;
      }
    }

    if (type === 1) {
      if (route.types?.includes(type)) {
        if (route.children) {
          route.children = getRoutes(route.children, type, role);
        }
        return [...memo, route];
      } else {
        return memo;
      }
    }
  }, []);
};

export default authRoutes;
