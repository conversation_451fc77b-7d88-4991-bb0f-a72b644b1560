import axios, { AxiosRequestConfig } from "axios";
import { DataApi } from "./data.api";
import { message } from "antd";
import { TOKEN_KEY } from "@/config";
export const isDev = process.env.NODE_ENV === "development";
export const deviceHost = `${window.location.host}`;

const requestInterceptor = (request: AxiosRequestConfig): any => {
  const token = JSON.parse(window.localStorage.getItem(TOKEN_KEY));
  if (request.url === "/api/console/common/login") {
    return request;
  }
  if (!token) {
    window.location.href = `${window.location.origin}${window.location.pathname}#/login`;
    return request;
  }
  request.headers = request.headers ?? {};
  request.headers = {
    ...request.headers,
    Authorization: token ? `Bearer ${token}` : "",
  };
  return request;
};

const axiosInstance = (path: string, withRequestInterceptor = true) => {
  const instance = axios.create({
    baseURL: `//${deviceHost}${isDev ? "/server" : ""}${path}`,
  });
  if (withRequestInterceptor) {
    instance.interceptors.request.use(requestInterceptor);
  }
  return instance;
};

const dataInstance = axiosInstance("");
const dataApi = new DataApi(dataInstance, {
  onHttpStatusError(status) {
    message.error(status);
  },
  onAppStatusError(code, msg) {
    if (code === "900008" || code === "900002") {
      window.location.href = `${window.location.origin}${window.location.pathname}#/login`;
    } else if (code === "900010") {
      // 校验错误不在这里显示消息，让组件自己处理
      // message.error(msg);
    } else {
      message.error(msg);
    }
  },
});

export { dataApi };
