import {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from "axios";
import { IAppResponse, IPaged } from "./common.model";

export class BaseApi {
  constructor(
    private axios: AxiosInstance,
    private errorHandler?: ApiErrorHandler
  ) {}

  async get<T>(path: string, params?: any) {
    const resp = this.axios.get<IAppResponse<T>>(path, { params });
    return this.handleResponse(resp);
  }

  async listPaged<T>(path: string, params?: any) {
    const resp = this.axios.get<IAppResponse<IPaged<T>>>(path, { params });
    return this.handleResponse(resp);
  }

  async list<T>(path: string, params?: any) {
    const resp = this.axios.get<IAppResponse<T>>(path, { params });
    return this.handleResponse(resp);
  }

  async delete<T>(path: string, params?: any) {
    const resp = this.axios.delete<IAppResponse<T>>(path, { params });
    return this.handleResponse(resp);
  }

  async post<T>(
    path: string,
    data: any,
    params?: any,
    config?: AxiosRequestConfig
  ) {
    const resp = this.axios.post<IAppResponse<T>>(path, data, {
      params,
      ...config,
    });
    return this.handleResponse(resp);
  }

  async postForm<T>(path: string, formData: FormData, params?: any) {
    const resp = this.axios.post<IAppResponse<T>>(path, formData, {
      params,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return this.handleResponse(resp);
  }

  async patch<T>(path: string, data: any, params?: any) {
    const resp = this.axios.patch<IAppResponse<T>>(path, data, {
      params,
    });
    return this.handleResponse(resp);
  }

  async put<T>(path: string, data: any) {
    const resp = this.axios.put<IAppResponse<T>>(path, data);
    return this.handleResponse(resp);
  }

  async upload<T>(path: string, data: any, config?: AxiosRequestConfig) {
    const resp = this.axios.post<IAppResponse<T>>(path, data, { ...config });
    return this.handleResponse(resp);
  }

  async handleResponse<T>(response: Promise<AxiosResponse<IAppResponse<T>>>) {
    try {
      const axiosResponse = await response;
      const result = this.handleAppError(axiosResponse.data);
      return result.data;
    } catch (e: any) {
      throw this.handleError(e);
    }
  }

  handleAppError<T>(response: IAppResponse<T>) {
    if (!response?.success) {
      if (this.errorHandler?.onAppStatusError) {
        this.errorHandler.onAppStatusError(response.code, response.msg);
        // return response;
      }
      throw {
        isAxiosError: false,
        message: response.msg,
        data: response.data,
      };
    }
    return response;
  }

  handleError(error: AxiosError) {
    console.error("axios request error:", error);
    if (error.response) {
      // if (this.errorHandler?.onHttpStatusError) {
      const data: any = error.response.data;
      this.handleAppError(data);
      // console.log("HTTP 错误", this.errorHandler);
      // this.errorHandler.onHttpStatusError(
      //   error.response.status,
      //   "",
      //   error.response
      // );
      // }
    } else if (error.request) {
      if (this.errorHandler?.onNetworkError) {
        this.errorHandler.onNetworkError();
      }
    } else {
      console.error("axios request config error:", error);
    }
    return error
  }
}

export interface ApiErrorHandler {
  onNetworkError?(): void;
  onHttpStatusError?(
    status: number,
    message: string,
    response: AxiosResponse
  ): void;
  onAppStatusError?(status: string, message: string): void;
}
