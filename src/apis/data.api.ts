import axios, { AxiosInstance } from "axios";
import { A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "./base.api";
import {
  IExamUser,
  IExamUserParams,
  ILoginParam,
  IUserProfile,
  IExamUserReviewParams,
  IExamUserScheduleSummary,
  IExamUserScheduleSummaryParams,
  IExamAddress,
  IExamUserScheduleSaveParams,
  ISaveExamUserReceiveCertificateParam,
  ISysUser,
  IMenu,
  IRegisterParam,
  ISendMessage,
  IMessageReply,
  IReplyMessage,
  INoticePublish,
  ISaveNoticeDraft,
  ISummaryAnalysis,
  ILatestNotice,
  IEnterpriseRequireMaterials,
  IEnterpriseRequireMaterialsParams,
  ISaveEnterpriseRequireMaterials,
  IEditEnterpriseRequireMaterials,
  IEnableEnterpriseRequireMaterials,
  IApprovalFlow,
  IApprovalFlowListParams,
  ISaveApprovalFlow,
  IEditApprovalFlow,
  ITrainingAccount,
  IEditEnterpriseAccount,
  IResetEnterpriseAccountPwd,
  ICommonId,
  ISaveEnterpriseCustomer,
  IEditEnterpriseCustomer,
  ISaveRevokeEnterpriseSecurityGuard,
  ISaveEnterpriseSecurityGuard,
  IEditEnterpriseSecurityGuard,
} from "./data.model";
import { IPaged, IPageQueryParams } from "./common.model";
export class DataApi extends BaseApi {
  constructor(axiosInstance?: AxiosInstance, errorHandler?: ApiErrorHandler) {
    super(axiosInstance ?? axios.create(), errorHandler);
  }

  async postLogin(params: ILoginParam) {
    return this.post<any>(`/api/console/common/login`, params);
  }
  async postRegisterEnterprise(params: IRegisterParam) {
    return this.post<any>(`/api/console/common/registerEnterprise`, params);
  }
  async getUserProfile() {
    return this.get<IUserProfile>(`/api/console/common/getUserProfile`);
  }
  async getLoginOut() {
    return this.get(`/api/console/common/loginOut`);
  }
  async postFileUpload(file: File, type: number) {
    const formData = new FormData();
    formData.append("file", file);
    return this.postForm(`/api/console/file/upload`, formData, { type });
  }
  async postGetSysUserPage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<ISysUser>>(
      `/api/console/sysUser/getSysUserPage`,
      params
    );
  }
  async postGetSysUserId(id: string) {
    return this.post<any>(`/api/console/sysUser/getSysUserId`, {
      id,
    });
  }
  async postSaveSysUser(params: ISysUser) {
    return this.post(`/api/console/sysUser/saveSysUser`, params);
  }
  async postEditSysUser(params: ISysUser) {
    return this.post(`/api/console/sysUser/editSysUser`, params);
  }
  async postUpdateSysUserPwd(id: string, pwd: string) {
    return this.post(`/api/console/sysUser/updateSysUserPwd`, { id, pwd });
  }
  async postDelSysUser(id: string) {
    return this.post(`/api/console/sysUser/delSysUser`, { id });
  }
  async postGetExamUserPage(params: IExamUserParams) {
    return this.post<IPaged<IExamUser>>(
      `/api/console/examUser/getExamUserPage`,
      params
    );
  }
  async postGetExportExamUserList(params: any) {
    return this.post<Array<IExamUser>>(
      `/api/console/examUser/getExportExamUserList`,
      params
    );
  }
  async postGetExamUser(params: { id?: string; idCard?: string }) {
    return this.post<IExamUser>(`/api/console/examUser/getExamUser`, params);
  }
  async postGetExamUserSummary(id: string) {
    return this.post<IExamUser>(`/api/console/examUser/getExamUserSummary`, {
      id,
    });
  }
  async postSaveExamUser(params: IExamUser) {
    return this.post(`/api/console/examUser/saveExamUser`, params);
  }
  async postBatchSaveExamUser(params: Array<IExamUser>) {
    return this.post(`/api/console/examUser/batchSaveExamUser`, params);
  }
  async postEditExamUser(params: IExamUser) {
    return this.post(`/api/console/examUser/editExamUser`, params);
  }
  async postDelExamUser(id: string) {
    return this.post(`/api/console/examUser/delExamUser`, {
      id,
    });
  }
  async postGetReviewExamUser(id: string) {
    return this.post<IExamUser>(`/api/console/examUser/getReviewExamUser`, {
      id,
    });
  }
  async postSaveReviewExamUser(params: IExamUserReviewParams) {
    return this.post(`/api/console/examUser/saveReviewExamUser`, params);
  }
  async postReAuditExamUser(id: string) {
    return this.post(`/api/console/examUser/reAuditExamUser`, { id });
  }
  async postGetExamAddressList() {
    return this.post<Array<IExamAddress>>(
      `/api/console/examAddress/getExamAddressList`,
      {}
    );
  }
  async postSaveExamAddress(name: string) {
    return this.post(`/api/console/examAddress/saveExamAddress`, { name });
  }
  async postDelExamAddress(id: string) {
    return this.post(`/api/console/examAddress/delExamAddress`, { id });
  }
  async postGetExamUserScheduleSummaryPage(
    params: IExamUserScheduleSummaryParams
  ) {
    return this.post<IPaged<IExamUserScheduleSummary>>(
      `/api/console/examUserSchedule/getExamUserScheduleSummaryPage`,
      params
    );
  }
  async postSaveExamUserSchedule(params: IExamUserScheduleSaveParams) {
    return this.post(
      `/api/console/examUserSchedule/saveExamUserSchedule`,
      params
    );
  }
  async postUpdateExamUserSchedule(params: IExamUserScheduleSaveParams) {
    return this.post(
      `/api/console/examUserSchedule/updateExamUserSchedule`,
      params
    );
  }
  async postGetExamUserSchedulePage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<any>>(
      `/api/console/examUserSchedule/getExamUserSchedulePage`,
      params
    );
  }
  async postGetExportExamUserScheduleList(params: any) {
    return this.post<Array<any>>(
      `/api/console/examUserSchedule/getExportExamUserScheduleList`,
      params
    );
  }
  async postSaveExamUser2ExamUserSchedule(
    examUserScheduleId: string,
    examUserIds: Array<string>
  ) {
    return this.post(
      `/api/console/examUserSchedule/saveExamUser2ExamUserSchedule`,
      { examUserScheduleId, examUserIds }
    );
  }
  async postDelExamUser2ExamUserSchedule(
    examUserScheduleId: string,
    examUserIds: Array<string>
  ) {
    return this.post(
      `/api/console/examUserSchedule/delExamUser2ExamUserSchedule`,
      { examUserScheduleId, examUserIds }
    );
  }
  async postSaveImportExamUser2ExamUserSchedule(
    examUserScheduleId: string,
    examUserIdcards: Array<string>
  ) {
    return this.post(
      `/api/console/examUserSchedule/saveImportExamUser2ExamUserSchedule`,
      { examUserScheduleId, examUserIdcards }
    );
  }
  async postGetPendingExamUserList(params: {
    examUserScheduleId: string;
    level: number;
    registrationUnit?: string;
    search?: string;
  }) {
    return this.post<Array<any>>(
      `/api/console/examUser/getPendingExamUserList`,
      params
    );
  }
  async postGetExamUserAdmissionPage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<any>>(
      `/api/console/examUserSchedule/getExamUserAdmissionPage`,
      params
    );
  }
  async postGetPrintExamUserAdmissionList(
    examUserScheduleId: string,
    examUserIds: Array<string>
  ) {
    return this.post<any[]>(
      `/api/console/examUserSchedule/getPrintExamUserAdmissionList`,
      { examUserScheduleId, examUserIds }
    );
  }
  async postGetExamUserScorePage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<any>>(
      `/api/console/examUserSchedule/getExamUserScorePage`,
      params
    );
  }
  async postGetExportExamUserScoreList(params: any) {
    return this.post<Array<any>>(
      `/api/console/examUserSchedule/getExportExamUserScoreList`,
      params
    );
  }
  async postSaveExamUserPhysicalStatus(id: string, physicalStatus: number) {
    return this.post(
      `/api/console/examUserSchedule/saveExamUserPhysicalStatus`,
      { id, physicalStatus }
    );
  }
  async postGetExamUserScheduleRecordLogPage<T extends IPageQueryParams>(
    params: T
  ) {
    return this.post<IPaged<any>>(
      `/api/console/examUserSchedule/getExamUserScheduleRecordLogPage`,
      params
    );
  }
  async postGetExamUserScheduleCertificateSummaryPage<
    T extends IPageQueryParams,
  >(params: T) {
    return this.post<IPaged<any>>(
      `/api/console/examUserSchedule/getExamUserScheduleCertificateSummaryPage`,
      params
    );
  }
  async postGetExamUserCertificatePage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<any>>(
      `/api/console/examUserSchedule/getExamUserCertificatePage`,
      params
    );
  }
  async postGetPrintExamUserCertificate(id: string) {
    return this.post<any>(
      `/api/console/examUserSchedule/getPrintExamUserCertificate`,
      { id }
    );
  }
  async postGetExamUserReceiveCertificatePage<T extends IPageQueryParams>(
    params: T
  ) {
    return this.post<IPaged<any>>(
      `/api/console/examUserSchedule/getExamUserReceiveCertificatePage`,
      params
    );
  }
  async postSaveExamUserReceiveCertificate(
    params: ISaveExamUserReceiveCertificateParam
  ) {
    return this.post(
      `/api/console/examUserSchedule/saveExamUserReceiveCertificate`,
      params
    );
  }
  async getUserMenuList() {
    return this.get<IMenu[]>(`/api/console/common/getUserMenuList`);
  }

  // 留言管理
  async postGetSendMessagePage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<ISendMessage>>(
      `/api/console/message/getSendMessagePage`,
      params
    );
  }
  async postSaveMessage(params: { content: string }) {
    return this.post(`/api/console/message/saveMessage`, params);
  }
  async postDelMessage(id: string) {
    return this.post(`/api/console/message/delMessage`, { id });
  }
  async postGetMessageReplyList(params: { id: string }) {
    return this.post<IMessageReply>(
      `/api/console/message/getMessageReplyList`,
      params
    );
  }
  async postSaveMessageReply(params: { messageId: string; content: string }) {
    return this.post<IMessageReply>(
      `/api/console/message/saveMessageReply`,
      params
    );
  }

  async postGetMessagePage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<IReplyMessage>>(
      `/api/console/message/getMessagePage`,
      params
    );
  }

  // 通知通告管理
  async getNoticePublishPage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<INoticePublish>>(
      `/api/console/notice/getNoticePublishPage`,
      params
    );
  }

  // 通知通告详情
  async getNoticePublish(id: string) {
    return this.post<any>(`/api/console/notice/getNoticePublish`, { id });
  }

  // 待选企业列表
  async getSearchNoticePublishEnterpriseList(search?: string) {
    return this.post<any>(
      `/api/console/notice/getSearchNoticePublishEnterpriseList`,
      { search }
    );
  }

  // 企业列表
  async getNoticePublishEnterpriseList(params: { id: string; type: number }) {
    return this.post<any>(
      `/api/console/notice/getNoticePublishEnterpriseList`,
      params
    );
  }

  // 保存通知通告草稿
  async saveNoticeDraft(params: ISaveNoticeDraft) {
    return this.post<any>(`/api/console/notice/saveNoticeDraft`, params);
  }

  // 保存通知通告草稿
  async delNoticePublish(id: string) {
    return this.post<any>(`/api/console/notice/delNoticePublish`, { id });
  }

  // 保存发布通知通告
  async saveNoticePublish(id: string) {
    return this.post<any>(`/api/console/notice/saveNoticePublish`, { id });
  }

  // 撤回发布通知通告
  async withdrawNoticePublish(id: string) {
    return this.post<any>(`/api/console/notice/withdrawNoticePublish`, { id });
  }

  // 通知通告列表
  async getNoticeViewPage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<INoticePublish>>(
      `/api/console/notice/getNoticeViewPage`,
      params
    );
  }

  // 通知通告详情
  async getNoticeView(id: string) {
    return this.post<any>(`/api/console/notice/getNoticeView`, { id });
  }

  // 统计-列表概括
  async getPageStatisticsSummary(type: number) {
    return this.post<any>(`/api/console/summary/getPageStatisticsSummary`, {
      type,
    });
  }

  // 统计-通知公告概括
  async getLatestContentList() {
    return this.post<ILatestNotice[]>(
      `/api/console/summary/getLatestContentList`,
      {}
    );
  }

  // 统计-考试数据概括
  async getSummaryAnalysis() {
    return this.post<ISummaryAnalysis>(
      `/api/console/summary/getSummaryAnalysis`,
      {}
    );
  }

  // 训练管理-训练账户列表
  async getExamTrainingAccountList(params: any) {
    return this.post<ITrainingAccount[]>(
      `/api/console/examTrainingAccount/getExamTrainingAccountList`,
      params
    );
  }

  // 训练管理-新增训练账户
  async saveExamTrainingAccount(params: any) {
    return this.post<any>(
      `/api/console/examTrainingAccount/saveExamTrainingAccount`,
      params
    );
  }

  // 训练管理-修改训练账户密码
  async updateExamTrainingAccountPwd(params: any) {
    return this.post<any>(
      `/api/console/examTrainingAccount/updateExamTrainingAccountPwd`,
      params
    );
  }

  // 训练管理-编辑训练账户
  async editExamTrainingAccount(params: any) {
    return this.post<any>(
      `/api/console/examTrainingAccount/editExamTrainingAccount`,
      params
    );
  }

  async postGetSetupApplication() {
    return this.post<any>(`/api/console/enterprise/getSetupApplication`, {});
  }
  async postGetFullSetupApplication(id: string) {
    return this.post<any>(`/api/console/enterprise/getFullSetupApplication`, {
      id,
    });
  }
  async postGetSetupApprovalPage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<any>>(
      `/api/console/enterprise/getSetupApprovalPage`,
      params
    );
  }
  async postWithdrawSetupApplication(id: string) {
    return this.post(`/api/console/enterprise/withdrawSetupApplication`, {
      id,
    });
  }
  async postGetRegionList(params: { type: number; code?: string }) {
    return this.post<any>(`/api/console/region/getRegionList`, params);
  }

  // 保存企业设立申请草稿
  async saveSetupApplicationDraft(params: any) {
    return this.post<any>(
      `/api/console/enterprise/saveSetupApplicationDraft`,
      params
    );
  }

  // 提交企业设立申请
  async submitSetupApplication(params: { id: string }) {
    return this.post<any>(
      `/api/console/enterprise/submitSetupApplication`,
      params
    );
  }

  // 保存企业设立审核
  async saveSetupApproval(params: {
    id: string;
    bizStatus: 1 | 2;
    checkContent?: string;
  }) {
    return this.post<any>(`/api/console/enterprise/saveSetupApproval`, params);
  }
  // 保存企业变更审核
  async saveChangeApproval(params: {
    id: string;
    bizStatus: 1 | 2;
    checkContent?: string;
  }) {
    return this.post<any>(
      `/api/console/enterpriseChange/saveChangeApproval`,
      params
    );
  }

  // 审批资料相关API
  async postGetEnterpriseRequireMaterialsList(
    params: IEnterpriseRequireMaterialsParams
  ) {
    return this.post<Array<IEnterpriseRequireMaterials>>(
      `/api/console/enterpriseRequireMaterials/getEnterpriseRequireMaterialsList`,
      params
    );
  }

  async postGetEnterpriseRequireMaterials(id: string) {
    return this.post<IEnterpriseRequireMaterials>(
      `/api/console/enterpriseRequireMaterials/getEnterpriseRequireMaterials`,
      { id }
    );
  }

  async postSaveEnterpriseRequireMaterials(
    params: ISaveEnterpriseRequireMaterials
  ) {
    return this.post<any>(
      `/api/console/enterpriseRequireMaterials/saveEnterpriseRequireMaterials`,
      params
    );
  }

  async postEditEnterpriseRequireMaterials(
    params: IEditEnterpriseRequireMaterials
  ) {
    return this.post<any>(
      `/api/console/enterpriseRequireMaterials/editEnterpriseRequireMaterials`,
      params
    );
  }

  async postEnableEnterpriseRequireMaterials(
    params: IEnableEnterpriseRequireMaterials
  ) {
    return this.post<any>(
      `/api/console/enterpriseRequireMaterials/enableEnterpriseRequireMaterials`,
      params
    );
  }

  async postDelEnterpriseRequireMaterials(id: string) {
    return this.post<any>(
      `/api/console/enterpriseRequireMaterials/delEnterpriseRequireMaterials`,
      { id }
    );
  }

  // 审批流程相关API
  async postGetApprovalFlowList(params: IApprovalFlowListParams) {
    return this.post<Array<IApprovalFlow>>(
      `/api/console/approvalFlow/getApprovalFlowList`,
      params
    );
  }

  async postGetApprovalFlow(id: string) {
    return this.post<IApprovalFlow>(
      `/api/console/approvalFlow/getApprovalFlow`,
      { id }
    );
  }

  async postSaveApprovalFlow(params: ISaveApprovalFlow) {
    return this.post<any>(`/api/console/approvalFlow/saveApprovalFlow`, params);
  }

  async postEditApprovalFlow(params: IEditApprovalFlow) {
    return this.post<any>(`/api/console/approvalFlow/editApprovalFlow`, params);
  }

  // 企业信息变更申请相关API
  async postGetInitLegalRepresentative() {
    return this.post<any>(
      `/api/console/enterpriseChange/getInitLegalRepresentative`,
      {}
    );
  }

  async postGetInitShareholders() {
    return this.post<any>(
      `/api/console/enterpriseChange/getInitShareholders`,
      {}
    );
  }

  async postGetChangeApplicationPage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<any>>(
      `/api/console/enterpriseChange/getChangeApplicationPage`,
      params
    );
  }

  async postGetFullChangeApplication(id: string) {
    return this.post<any>(
      `/api/console/enterpriseChange/getFullChangeApplication`,
      {
        id,
      }
    );
  }

  async saveChangeApplicationDraft(params: any) {
    return this.post<any>(
      `/api/console/enterpriseChange/saveChangeApplicationDraft`,
      params
    );
  }

  async submitChangeApplication(params: { id: string }) {
    return this.post<any>(
      `/api/console/enterpriseChange/submitChangeApplication`,
      params
    );
  }

  async postWithdrawChangeApplication(id: string) {
    return this.post(
      `/api/console/enterpriseChange/withdrawChangeApplication`,
      {
        id,
      }
    );
  }

  async postGetChangeApprovalPage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<any>>(
      `/api/console/enterpriseChange/getChangeApprovalPage`,
      params
    );
  }

  async postGetEnterpriseServiceTypeSummaryList() {
    return this.post<any>(
      `/api/console/enterprise/getEnterpriseServiceTypeSummaryList`,
      {}
    );
  }

  // getEnterpriseAccountPage
  async postGetEnterpriseAccountPage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<any>>(
      `/api/console/enterpriseAccount/getEnterpriseAccountPage`,
      params
    );
  }

  // 编辑企业账号
  async postEditEnterpriseAccount(params: IEditEnterpriseAccount) {
    return this.post<any>(
      `/api/console/enterpriseAccount/editEnterpriseAccount`,
      params
    );
  }

  // 重置企业账号密码
  async postResetEnterpriseAccountPwd(params: IResetEnterpriseAccountPwd) {
    return this.post<any>(
      `/api/console/enterpriseAccount/resetEnterpriseAccountPwd`,
      params
    );
  }

  // 注销企业账号
  async postFreezeEnterpriseAccount(params: ICommonId) {
    return this.post<any>(
      `/api/console/enterpriseAccount/freezeEnterpriseAccount`,
      params
    );
  }

  // 删除企业账号
  async postDelEnterpriseAccount(params: ICommonId) {
    return this.post<any>(
      `/api/console/enterpriseAccount/delEnterpriseAccount`,
      params
    );
  }

  async postGetEnterpriseCustomerSummaryPage<T extends IPageQueryParams>(
    params: T
  ) {
    return this.post<IPaged<any>>(
      `/api/console/enterpriseCustomer/getEnterpriseCustomerSummaryPage`,
      params
    );
  }

  async postGetEnterpriseCustomerPage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<any>>(
      `/api/console/enterpriseCustomer/getEnterpriseCustomerPage`,
      params
    );
  }

  async postDelEnterpriseCustomer(params: ICommonId) {
    return this.post<any>(
      `/api/console/enterpriseCustomer/delEnterpriseCustomer`,
      params
    );
  }

  // 获取企业客户详情
  async postGetEnterpriseCustomer(params: ICommonId) {
    return this.post<any>(
      `/api/console/enterpriseCustomer/getEnterpriseCustomer`,
      params
    );
  }

  // 保存企业客户
  async postSaveEnterpriseCustomer(params: ISaveEnterpriseCustomer) {
    return this.post<any>(
      `/api/console/enterpriseCustomer/saveEnterpriseCustomer`,
      params
    );
  }

  // 编辑企业客户
  async postEditEnterpriseCustomer(params: IEditEnterpriseCustomer) {
    return this.post<any>(
      `/api/console/enterpriseCustomer/editEnterpriseCustomer`,
      params
    );
  }

  async postGetEnterpriseSecurityGuardSummaryPage<T extends IPageQueryParams>(
    params: T
  ) {
    return this.post<IPaged<any>>(
      `/api/console/securityGuard/getEnterpriseSecurityGuardSummaryPage`,
      params
    );
  }

  async postGetEnterpriseSecurityGuardSummaryList(id: string) {
    return this.post<any>(
      `/api/console/securityGuard/getEnterpriseSecurityGuardSummaryList`,
      { id }
    );
  }

  async postGetEnterpriseSecurityGuardPage<T extends IPageQueryParams>(
    params: T
  ) {
    return this.post<IPaged<any>>(
      `/api/console/securityGuard/getEnterpriseSecurityGuardPage`,
      params
    );
  }

  async postGetEnterpriseSecurityGuard(id: string) {
    return this.post<any>(
      `/api/console/securityGuard/getEnterpriseSecurityGuard`,
      { id }
    );
  }

  // 吊销保安员证书
  async postSaveRevokeEnterpriseSecurityGuard(
    params: ISaveRevokeEnterpriseSecurityGuard
  ) {
    return this.post<any>(
      `/api/console/securityGuard/saveRevokeEnterpriseSecurityGuard`,
      params
    );
  }

  // 保存保安人员
  async postSaveEnterpriseSecurityGuard(params: ISaveEnterpriseSecurityGuard) {
    return this.post<any>(
      `/api/console/securityGuard/saveEnterpriseSecurityGuard`,
      params
    );
  }

  // 编辑保安人员
  async postEditEnterpriseSecurityGuard(params: IEditEnterpriseSecurityGuard) {
    return this.post<any>(
      `/api/console/securityGuard/editEnterpriseSecurityGuard`,
      params
    );
  }

  async postDelEnterpriseSecurityGuard(id: string) {
    return this.post<any>(
      `/api/console/securityGuard/delEnterpriseSecurityGuard`,
      { id }
    );
  }

  async postRemoveResignedEnterpriseSecurityGuard(id: string) {
    return this.post<any>(
      `/api/console/securityGuard/removeResignedEnterpriseSecurityGuard`,
      { id }
    );
  }

  async postGetEnterpriseSecurityGuardList(params) {
    return this.post<Array<any>>(
      `/api/console/securityGuard/getEnterpriseSecurityGuardList`,
      params
    );
  }

  async postSaveResignedEnterpriseSecurityGuard(params) {
    return this.post<any>(
      `/api/console/securityGuard/saveResignedEnterpriseSecurityGuard`,
      params
    );
  }

  async postGetExportEnterpriseSecurityGuardList(params) {
    return this.post<Array<any>>(
      `/api/console/securityGuard/getExportEnterpriseSecurityGuardList`,
      params
    );
  }

  async postGetEnterpriseLicensePage<T extends IPageQueryParams>(params: T) {
    return this.post<IPaged<any>>(
      `/api/console/enterprise/getEnterpriseLicensePage`,
      params
    );
  }

  async postGetFullEnterprise(id) {
    return this.post<any>(`/api/console/enterprise/getFullEnterprise`, { id });
  }

  async postGetEnterpriseAnnualReviewPage<T extends IPageQueryParams>(
    params: T
  ) {
    return this.post<IPaged<any>>(
      `/api/console/enterprise/getEnterpriseAnnualReviewPage`,
      params
    );
  }

  async postGetEnterpriseAnnualReviewRecordList(id) {
    return this.post<Array<any>>(
      `/api/console/enterprise/getEnterpriseAnnualReviewRecordList`,
      { id }
    );
  }

  async postSaveEnterpriseAnnualReviewRecord(id) {
    return this.post(
      `/api/console/enterprise/saveEnterpriseAnnualReviewRecord`,
      { id }
    );
  }

  async postGetPageStatisticsSummary(type) {
    return this.post<Array<any>>(
      `/api/console/summary/getPageStatisticsSummary`,
      { type }
    );
  }
}
