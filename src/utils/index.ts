import * as XLSX from "xlsx";
import JSZip from "jszip";

export const getBase64 = (file: any): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

export const base64ToImg = (base64Data: string, fileName: string): File => {
  // 对 Base64 数据进行解码
  const byteCharacters = atob(base64Data);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  // 创建 Uint8Array 类型的字节数组
  const byteArray = new Uint8Array(byteNumbers);
  // 使用字节数组创建 Blob 对象
  const blob = new Blob([byteArray], { type: "image/png" });
  // 使用 Blob 对象创建 File 对象
  return new File([blob], fileName + ".png", { type: "image/png" });
};

/**
 * 解析 Excel 数据 (支持图片)
 * @param file Excel 文件
 * @param options.headerRows 表头占用的行数
 * @param options.header 表头列, 如 ["name", "age", "photo"]
 * @param options.imageCols 图片列, 如 ["photo"], 图片会转换成 File 对象
 * @returns 排除表头的行数据
 */
export const excelToData = (
  file: File,
  options: {
    headerRows: number;
    header: Array<string>;
    imageCols?: Array<string>;
  }
): Promise<Array<any>> =>
  new Promise(async (resolve, reject) => {
    try {
      const images = await getExcelImage(file);

      const reader = new FileReader();
      reader.onerror = (error) => reject(error);
      reader.onload = (e) => {
        const data = e.target.result;
        // 读取第一个工作表
        const workbook = XLSX.read(data, { type: "binary" });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        // 将工作表转换为 JSON 数据
        const rows = XLSX.utils.sheet_to_json(worksheet, {
          header: options.header,
        });
        // 删除表头行
        rows.splice(0, options.headerRows);
        // 设置图片
        let imageIdx = 0;
        rows.forEach((row) => {
          options.imageCols?.forEach((col) => {
            row[col] = images[imageIdx];
            imageIdx++;
          });
        });
        resolve(rows);
      };
      reader.readAsArrayBuffer(file);
    } catch (error) {
      reject(error);
    }
  });

/**
 * 获取 Excel 中的图片
 * @param file Excel 文件
 * @returns 图片 base64 数组
 */
const getExcelImage = async (file: File): Promise<Array<File>> => {
  const images = [];
  const zip = new JSZip();

  let zipLoadResult = await zip.loadAsync(file); // 将xlsx文件转zip文件
  for (const key in zipLoadResult.files) {
    const file = zipLoadResult.files[key];
    if (key.indexOf("media/image") != -1 && !file.dir) {
      await zip
        .file(file.name)
        .async("base64")
        .then((base64) => {
          images.push(base64ToImg(base64, file.name));
        });
    }
  }
  return images;
};

// 计算每列的最大宽度
const getColumnWidths = (data) => {
  const columnWidths = [];
  for (let col = 0; col < data[0].length; col++) {
    let maxLength = 0;
    for (let row = 0; row < data.length; row++) {
      const cellValue = String(data[row][col]);
      // 计算中文占比
      const chineseCharacters = cellValue.match(/[\u4e00-\u9fa5]/g);
      const chineseCount = chineseCharacters ? chineseCharacters.length : 0;
      const totalLength = cellValue.length;
      const chinesePercentage =
        totalLength > 0 ? chineseCount / totalLength : 0;
      maxLength = Math.max(
        maxLength,
        cellValue.length * (1 + chinesePercentage * 0.8)
      );
    }
    // 适当增加一些宽度以确保内容显示完整
    columnWidths[col] = maxLength + 2;
  }
  return columnWidths;
};

/**
 * 数据转 Excel 并下载
 * @param rows 数据行
 * @param options.header 表头和数据列的映射
 * @param options.filename 文件名, 不含扩展名
 */
export const dataToExcel = (
  rows: Array<any>,
  options: {
    header: Array<{ value: string; label: string }>;
    filename?: string;
  }
): void => {
  const headerRow = options.header.map((x) => x.label);
  const dataRows = rows.map((row) => {
    return options.header.map(({ value }) => row[value]);
  });
  const data = [headerRow, ...dataRows];
  const columnWidths = getColumnWidths(data);
  // 创建工作簿
  const ws = XLSX.utils.aoa_to_sheet(data);
  ws["!cols"] = columnWidths.map((width) => ({ wch: width }));
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
  // 导出 Excel 文件
  XLSX.writeFile(wb, `${options.filename ?? "导出数据"}.xlsx`);
};

export const isPathMatchRoute = (
  pathname: string,
  routePath: string
): boolean => {
  const routeRegex = new RegExp(
    `^${routePath.replace(/:[^\/]+/g, "([^/]+)")}$`
  );
  return routeRegex.test(pathname);
};

export const getFileTypeFromURL = (url: string) => {
  const urlObj = new URL(url);
  const filePath = urlObj.searchParams.get("file");
  if (!filePath) {
    return null;
  }
  const match = filePath.match(/\.([^.]+)$/);
  if (match) {
    return match[1];
  }
  return null;
};

export const downloadFile = async (url: string, name: string) => {
  try {
    const response = await fetch(url);
    const blob = await response.blob();
    const a = document.createElement("a");
    a.href = URL.createObjectURL(blob);
    a.download = name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(a.href);
  } catch (error) {
    console.error("下载文件失败:", error);
  }
};

export const findObjectsById = (nodes: any[], targetIds: string[]): any[] => {
  let result: any[] = [];
  for (const node of nodes) {
    if (targetIds.includes(node.id)) {
      result.push(node);
    }
    if (node.children && node.children.length > 0) {
      result = result.concat(findObjectsById(node.children, targetIds));
    }
  }
  return result;
};

export const countLevelTwoNodes = (nodes: any[], currentLevel = 0): number => {
  let count = 0;
  for (const node of nodes) {
    if (currentLevel === 2) {
      count++;
    } else if (node.children && node.children.length > 0) {
      count += countLevelTwoNodes(node.children, currentLevel + 1);
    }
  }
  return count;
};
