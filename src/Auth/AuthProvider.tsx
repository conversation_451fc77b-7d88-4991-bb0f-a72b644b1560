import { useLocalStorageState } from "ahooks";
import { ReactNode, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { AuthContext } from "./AuthContext";
import { dataApi } from "@/apis";
import { IUserProfile } from "@/apis/data.model";
import { message } from "antd";
import { TOKEN_KEY } from "@/config";

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [token, setToken] = useLocalStorageState<string>(TOKEN_KEY);
  const [user, setUser] = useState<IUserProfile>();
  const [loginType, setLoginType] = useState(-1);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    if (window.location.pathname.includes("admin")) {
      setLoginType(0);
    }
    if (window.location.pathname.includes("enterprise")) {
      setLoginType(1);
    }
  }, []);

  useEffect(() => {
    if (!token) {
      if (window.location.hash === "/register") return;
      navigate("/login");
      return;
    }
    setLoading(true);
    dataApi.getUserProfile().then((data) => {
      setUser({
        ...data,
        role: data.role,
      });
      setLoading(false);
    });
  }, [token]);

  const login = (newToken: string) => {
    setToken(newToken);
    message.success("登录成功");
  };

  const logout = () => {
    dataApi.getLoginOut().then(() => {
      setToken(null);
      navigate("/login");
      message.success("退出登录");
    });
  };

  const value = {
    token,
    user,
    loading,
    login,
    logout,
    loginType,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
