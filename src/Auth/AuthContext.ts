import { IMenu, IUserProfile } from "@/apis/data.model";
import { createContext, useContext } from "react";

interface IAuthContextType {
  token: string;
  user: IUserProfile;
  loading: boolean;
  login: (token: string) => void;
  logout: () => void;
  loginType: number;
}

export const AuthContext = createContext<IAuthContextType>(
  {} as IAuthContextType
);

export const useAuth = () => {
  return useContext(AuthContext);
};
