import React from "react";
import { Divider, DividerProps } from "antd";
import "./style.less";
import { useAuth } from "@/Auth";

interface IPermDividerProps {
  types: number[];
  roles?: number[];
}

const PermButton: React.FC<IPermDividerProps & DividerProps> = (props) => {
  const { types, roles = [], children, ...restProps } = props;
  const { user } = useAuth();

  return (
    <>
      {user.type === 1 && types.includes(user.type) ? (
        <Divider {...restProps}>{children}</Divider>
      ) : null}

      {user.type === 0 && roles.includes(user.role) ? (
        <Divider {...restProps}>{children}</Divider>
      ) : null}
    </>
  );
};

export default PermButton;
