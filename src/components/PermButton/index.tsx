import React from "react";
import { Button, ButtonProps } from "antd";
import "./style.less";
import { useAuth } from "@/Auth";

// type 0: 管理员  1: 企业
// role 1: 一级管理员 2二级管理员
interface IPermButtonProps {
  types: number[];
  roles?: number[];
}

const PermButton: React.FC<IPermButtonProps & ButtonProps> = (props) => {
  const { types, roles = [], children, ...restProps } = props;
  const { user } = useAuth();

  return (
    <>
      {user.type === 1 && types.includes(user.type) ? (
        <Button {...restProps}>{children}</Button>
      ) : null}

      {user.type === 0 && roles.includes(user.role) ? (
        <Button {...restProps}>{children}</Button>
      ) : null}
    </>
  );
};

export default PermButton;
