import React, { ReactNode, useEffect, useState } from "react";
import "./style.less";
import { useAuth } from "@/Auth";

interface IPermViewProps {
  types: number[];
  roles?: number[];
  children?: ReactNode;
}

const PermView: React.FC<IPermViewProps> = (props) => {
  const { types, roles, children } = props;
  const { user } = useAuth();

  return (
    <>
      {user.type === 1 && types.includes(user.type) ? children : null}
      {user.type === 0 && roles.includes(user.role) ? children : null}
    </>
  );
};

export default PermView;
