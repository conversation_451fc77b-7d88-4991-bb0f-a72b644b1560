import React, { useEffect, useState, useContext } from "react";
import { Cascader, Spin } from "antd";
import { dataApi } from "@/apis";
import type { DefaultOptionType } from "antd/es/cascader";
import DisabledContext from "antd/es/config-provider/DisabledContext";

interface RegionCascaderProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  placeholder?: string;
  allowClear?: boolean;
  style?: React.CSSProperties;
  [key: string]: any;
}

const RegionCascader: React.FC<RegionCascaderProps> = ({
  value,
  onChange,
  placeholder = "请选择省/市/县",
  allowClear = true,
  style,
  ...rest
}) => {
  const [options, setOptions] = useState<DefaultOptionType[]>([]);
  const [loading, setLoading] = useState(false);
  const [initialized, setInitialized] = useState(false);

  const contextDisabled = useContext(DisabledContext);
  const mergedDisabled = rest.disabled ?? contextDisabled;

  // 加载地区数据的通用函数
  const loadRegionData = async (type: number, code?: string): Promise<DefaultOptionType[]> => {
    try {
      const data = await dataApi.postGetRegionList({ type, code });
      return data.map((item: any) => ({
        value: item.code,
        label: item.name,
        isLeaf: type === 2, // 县级是最后一级
      }));
    } catch (error) {
      console.error(`加载${type === 0 ? '省份' : type === 1 ? '城市' : '县区'}数据失败:`, error);
      return [];
    }
  };

  // 初始化省份数据
  const initProvinces = async () => {
    if (options.length > 0) return options;

    setLoading(true);
    try {
      const provinces = await loadRegionData(0);
      setOptions(provinces);
      return provinces;
    } finally {
      setLoading(false);
    }
  };

  // 级联加载下级数据
  const loadData = async (selectedOptions: DefaultOptionType[]) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;

    try {
      const type = selectedOptions.length === 1 ? 1 : 2;
      const children = await loadRegionData(type, targetOption.value as string);
      targetOption.children = children;
    } finally {
      targetOption.loading = false;
      setOptions([...options]);
    }
  };

  // 初始化选中值对应的选项数据
  const initializeValue = async (codes: string[], provinceOptions?: DefaultOptionType[]) => {
    if (!codes?.length || initialized) return;

    setInitialized(true);
    setLoading(true);

    try {
      const currentOptions = provinceOptions || options;
      if (!currentOptions.length) return;

      // 找到省份
      const province = currentOptions.find(opt => opt.value === codes[0]);
      if (!province || codes.length === 1) return;

      // 加载并设置城市数据
      const cities = await loadRegionData(1, codes[0]);
      province.children = cities;

      if (codes.length === 2) {
        setOptions([...currentOptions]);
        return;
      }

      // 找到城市并加载县区数据
      const city = cities.find(opt => opt.value === codes[1]);
      if (city) {
        const counties = await loadRegionData(2, codes[1]);
        city.children = counties;
      }

      setOptions([...currentOptions]);
    } catch (error) {
      console.error('初始化选中值失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时初始化
  useEffect(() => {
    const init = async () => {
      const provinces = await initProvinces();
      if (value?.length) {
        await initializeValue(value, provinces);
      }
    };
    init();
  }, []);

  // 监听 value 变化
  useEffect(() => {
    if (value?.length && options.length && !initialized) {
      initializeValue(value);
    }
  }, [value, options.length]);

  return (
    <Spin spinning={loading}>
      <Cascader
        options={options}
        loadData={loadData}
        onChange={onChange}
        value={value}
        placeholder={placeholder}
        allowClear={allowClear}
        style={style}
        changeOnSelect
        disabled={mergedDisabled || loading}
        {...rest}
      />
    </Spin>
  );
};

export default RegionCascader;
