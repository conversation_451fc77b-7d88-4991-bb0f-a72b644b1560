import { FC } from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>lex, Row } from "antd";
import "./index.less";

interface TextPanelProps {
  title: string;
  action?: React.ReactNode;
  children?: React.ReactNode;
}

const TextPanel: FC<TextPanelProps> = (props) => {
  const { title, action, children } = props;
  return (
    <Flex vertical className="title-panel" gap={20}>
      <Flex justify="space-between" align="center" style={{ height: "32px" }}>
        <span className="title-panel-text">{title}</span>
        {action}
      </Flex>
      <Flex vertical className="title-panel-content">{children}</Flex>
    </Flex>
  );
};

export default TextPanel;
