.home {
  width: 100%;
  height: 100%;

  .quick-item {
    height: 100px;
    line-height: 20px;
    border-radius: 10px;
    color: #101010;
    font-size: 14px;
    .quick-item-icon {
      font-size: 20px;
    }
  }
  .quick-item__1 {
    background: #ece5ff;
  }
  .quick-item__2 {
    background: #e8effb;
  }
  .quick-item__3 {
    background: #e9fcf9;
  }
  .quick-item__4 {
    background: #ffeeef;
  }
  .analysis-pannel {
    .analysis-item {
      .analysis-item-wrapper {
        border-radius: 6px;
        border: 1px solid rgba(232, 232, 232, 1);
        padding: 16px 8px 16px 16px;
        .analysis-icon {
          width: 80px;
          height: 80px;
          background: #4070ff;
          border-radius: 6px;
          line-height: 20px;
          color: white;
          font-size: 30px;
        }
        .analysis-content {
          .analysis-item-title {
            height: 22px;
            line-height: 22px;
            color: rgba(153, 153, 153, 1);
            font-size: 16px;
          }
          .analysis-item-value {
            height: 40px;
            line-height: 40px;
            color: rgba(17, 19, 62, 1);
            font-size: 28px;
          }
        }
      }
    }
  }

  .point {
    width: 6px;
    height: 6px;
    background: #4070ff;
    border-radius: 50%;
    position: relative;
    top: 9px;
  }
  .system-summary {
    .exam-header {
      height: 40px;
      background: #5a9cf7;
      color: white;
      border-radius: 4px;
    }
    .exam-content {
      background: #f7fafe;
      padding: 24px 12px;
      line-height: 24px;
      height: 350px;
      overflow-y: auto;
    }
    .manage-header {
      height: 40px;
      background: #86d29c;
      color: white;
      border-radius: 4px;
    }
    .manage-content {
      background: #f2fef6;
      padding: 24px 12px;
      line-height: 24px;
      height: 350px;
      overflow-y: auto;
    }
    .message-header {
      height: 40px;
      background: #efa35c;
      color: white;
      border-radius: 4px;
    }
    .message-content {
      background: #fef7eb;
      padding: 24px 12px;
      line-height: 24px;
      height: 350px;
      overflow-y: auto;
    }
  }
}
