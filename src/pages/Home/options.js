export const getOption = (historys) => {
  const timeSet = new Set();
  const gpuTypes = [];
  const seriesMap = new Map();

  historys.forEach((item) => {
    gpuTypes.push(item.gpuType);
    const seriesData = [];

    item.history.forEach((h) => {
      timeSet.add(h.time);
      const visible = h.cost > 0;
      seriesData.push(visible ? h.cost : null);
    });

    seriesMap.set(item.gpuType, seriesData);
  });

  const xAxisData = Array.from(timeSet);

  const colorPalette = [
    {
      dark: `rgba(38,141,255,0.3)`,
      light: `rgba(38,141,255,0.1)`,
      color: `rgb(38,141,255)`,
    },
    {
      dark: `rgba(250,173,20,0.3)`,
      light: `rgba(250,173,20,0.1)`,
      color: `rgb(250,173,20)`,
    },
    {
      dark: `rgba(13, 227, 247,0.3)`,
      light: `rgba(13, 227, 247,0.1)`,
      color: `rgb(13, 227, 247)`,
    },
    {
      dark: `rgba(2, 57, 197, 0.3)`,
      light: `rgba(2, 57, 197,0.1)`,
      color: `rgb(2, 57, 197)`,
    },
  ];

  const getColor = (index) => {
    const hue = (index * 137.508) % 360;
    return {
      dark: `hsla(${hue}, 70%, 40%, 0.3)`,
      light: `hsla(${hue}, 70%, 90%, 0.1)`,
      color: `hsla(${hue}, 70%, 40%, 1)`,
    };
  };

  const getExtendedColorPalette = (index) => {
    if (index < colorPalette.length) {
      return colorPalette[index];
    }
    return getColor(index);
  };

  const series = [];
  gpuTypes.forEach((gpuType, index) => {
    const color = getExtendedColorPalette(index);
    series.push({
      name: gpuType,
      type: "line",
      smooth: true,
      stack: "total",
      areaStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: color.dark,
            },
            {
              offset: 1,
              color: color.light,
            },
          ],
        },
      },
      emphasis: {
        focus: "series",
      },
      lineStyle: {
        width: 2,
        curveness: 1,
        color: color.color,
      },
      symbolSize: 5,
      itemStyle: {
        color: color.color,
      },
      label: {
        color: color.color,
      },
      data: seriesMap.get(gpuType),
    });
  });

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
      formatter: (params) => {
        let total = 0;
        let tooltipContent = params
          .map((item) => {
            let cost = item.value;

            total = total + (cost ? cost : 0);
            return `${item.marker} ${item.seriesName}: ${
              cost ? cost.toFixed(2) : 0
            } G`;
          })
          .join("<br/>");
        return `使用总量: ${total.toFixed(2)} G<br/><b>${tooltipContent}</b>`;
      },
    },
    grid: {
      top: "5%",
      left: "5%",
      right: "5%",
      bottom: "10%",
      show: false,
    },
    legend: {
      icon: "circle",
      x: "right",
      padding: [0, 50],
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 20,
      textStyle: {
        fontSize: 14,
        color: "#3D3D3D",
      },
      data: gpuTypes,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: xAxisData,
    },
    yAxis: {
      type: "value",
      name: "[GB]",
    },
    series,
  };
};
