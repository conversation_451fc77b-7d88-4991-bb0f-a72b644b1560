import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Table,
  Button,
  Space,
  Modal,
  Select,
  Flex,
  type TableColumnsType,
  type TablePaginationConfig,
} from "antd";
import { dataApi } from "@/apis";
import { IExamUser } from "@/apis/data.model";
import dayjs from "dayjs";

interface LogModal {
  id: string;
  isLogModalVisible: boolean;
  setIsLogModalVisible: Dispatch<SetStateAction<boolean>>;
}

const LogModal: React.FC<LogModal> = (props) => {
  const { id, isLogModalVisible, setIsLogModalVisible } = props;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<Array<any>>([]);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [type, setType] = useState();

  useEffect(() => {
    if (isLogModalVisible && id) {
      getDataSourceRequest();
    }
  }, [isLogModalVisible, pagination]);

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetExamUserScheduleRecordLogPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        id,
        type,
      })
      .then((data) => {
        setLoading(false);
        setDataSource(data.items);
        setTotal(Number(data.total));
      })
      .finally(() => setLoading(false));
  };

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const onCancel = () => {
    setIsLogModalVisible(false);
    setTotal(0);
    setPagination({
      current: 1,
      pageSize: 20,
    });
  };

  const columns: TableColumnsType<IExamUser> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "IP",
      dataIndex: "ip",
      render: (value) => value || "-",
    },
    {
      title: "日志时间",
      dataIndex: "operatorTime",
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "操作类型",
      dataIndex: "type",
      render: (value) =>
        ({
          0: "初始考试",
          1: "加载试题",
          2: "答题",
          3: "提交试卷",
          4: "查看试卷答案",
          5: "查看分数",
          6: "退出考试",
          7: "切屏",
        })[value],
    },
    {
      title: "日志详情",
      dataIndex: "content",
      minWidth: 90,
      render: (value) => value || "-",
    },
  ];

  return (
    <Modal
      title="考试日志"
      open={isLogModalVisible}
      width={800}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          关闭
        </Button>,
      ]}
    >
      <Flex vertical gap={12}>
        <Space>
          <Select
            placeholder="请选择操作类型"
            allowClear
            style={{ width: 200 }}
            onChange={setType}
            options={[
              { value: "", label: "所有状态" },
              { value: 0, label: "初始考试" },
              { value: 1, label: "加载试题" },
              { value: 2, label: "答题" },
              { value: 3, label: "提交试卷" },
              { value: 4, label: "查看试卷答案" },
              { value: 5, label: "查看分数" },
              { value: 6, label: "退出考试" },
              { value: 7, label: "切屏" },
            ]}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 479px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
    </Modal>
  );
};

export default LogModal;
