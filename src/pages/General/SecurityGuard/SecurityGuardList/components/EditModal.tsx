import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Button,
  Form,
  Input,
  Modal,
  Select,
  InputNumber,
  Upload,
  Image,
  Flex,
  message,
  Typography,
  type UploadProps,
  Space,
} from "antd";
import { dataApi } from "@/apis";
import { fileUploadUrl, imageFallback } from "@/config";

interface EditModalProps {
  id?: string;
  isEditModalVisible: boolean;
  setIsEditModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
  mode: "add" | "edit" | "view";
  enterpriseId?: string;
}

const EditModal: React.FC<EditModalProps> = (props) => {
  const {
    id,
    isEditModalVisible,
    setIsEditModalVisible,
    tableReload,
    mode,
    enterpriseId,
  } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [photoUrl, setPhotoUrl] = useState<string>();
  const [certificateUrl, setcertificateUrl] = useState<string>();
  const [error, setError] = useState<string>();

  useEffect(() => {
    if (isEditModalVisible) {
      if (mode === "add") {
        // 添加模式，重置表单
        form.resetFields();
        setPhotoUrl(undefined);
        setcertificateUrl(undefined);
      } else if ((mode === "edit" || mode === "view") && id) {
        // 编辑或详情模式，获取详情数据
        dataApi.postGetEnterpriseSecurityGuard(id).then((data) => {
          form.setFieldsValue(data);
          setPhotoUrl(data.photoUrl);
          setcertificateUrl(data.certificateUrl);
        });
      }
    }
  }, [isEditModalVisible, mode, id]);

  const handlePhotoChange: UploadProps["onChange"] = ({ file }) => {
    if (file.status === "done") {
      form.setFieldValue("photo", file.response.data);
      setPhotoUrl(URL.createObjectURL(file.originFileObj));
    } else if (file.status === "removed") {
      form.setFieldValue("photo", null);
      setPhotoUrl(null);
    }
  };

  const handleCertificatePhotoChange: UploadProps["onChange"] = ({ file }) => {
    if (file.status === "done") {
      form.setFieldValue("certificatePath", file.response.data);
      setcertificateUrl(URL.createObjectURL(file.originFileObj));
    } else if (file.status === "removed") {
      form.setFieldValue("certificatePath", null);
      setcertificateUrl(null);
    }
  };

  const onFinish = (values: any) => {
    setLoading(true);
    setError("");

    const apiCall =
      mode === "add"
        ? dataApi.postSaveEnterpriseSecurityGuard(values)
        : dataApi.postEditEnterpriseSecurityGuard(values);

    apiCall
      .then(() => {
        message.success("操作成功");
        tableReload();
        onCancel();
      })
      .catch((err) => {
        setError(err.message);
        message.error(err.message);
      })
      .finally(() => setLoading(false));
  };

  const onCancel = () => {
    setIsEditModalVisible(false);
    form.resetFields();
    setPhotoUrl(undefined);
    setcertificateUrl(undefined);
  };

  // 根据模式设置标题
  const getTitle = () => {
    switch (mode) {
      case "add":
        return "添加人员信息";
      case "edit":
        return "编辑人员信息";
      case "view":
        return "人员信息详情";
      default:
        return "人员信息";
    }
  };

  return (
    <Modal
      title={
        <Space>
          <Typography.Text>{getTitle()}</Typography.Text>
          <Typography.Text type="danger">{error}</Typography.Text>
        </Space>
      }
      open={isEditModalVisible}
      width={800}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <div
        style={{
          maxHeight: "calc(100vh - 264px)",
          overflow: "auto",
          paddingRight: 24,
        }}
      >
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 120 } }}
          disabled={mode === "view"}
        >
          <Form.Item name="id" hidden>
            <Input />
          </Form.Item>
          <Form.Item
            name="name"
            label="姓名"
            rules={[{ required: true, message: "请输入姓名" }]}
          >
            <Input placeholder="请输入姓名" autoComplete="off" />
          </Form.Item>
          <Form.Item
            name="photo"
            label="照片"
            rules={[{ required: true, message: "请上传照片" }]}
          >
            <Flex align="center" gap={12}>
              <Image
                width={100}
                height={140}
                src={photoUrl}
                fallback={imageFallback}
              />
              <Space>
                <Upload
                  action={fileUploadUrl}
                  data={{ type: 6 }}
                  accept=".jpg,.jpeg,.png"
                  maxCount={1}
                  listType="picture"
                  showUploadList={false}
                  onChange={handlePhotoChange}
                >
                  <Button type="primary">上传照片</Button>
                </Upload>
                <div style={{ paddingLeft: 24 }}>建议大小：295*413</div>
              </Space>
            </Flex>
          </Form.Item>
          <Form.Item
            name="sex"
            label="性别"
            rules={[{ required: true, message: "请选择性别" }]}
          >
            <Select
              placeholder="请选择性别"
              allowClear
              options={[
                { value: 0, label: "女" },
                { value: 1, label: "男" },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="nationality"
            label="民族"
            rules={[{ required: true, message: "请输入民族" }]}
          >
            <Input placeholder="请输入民族" autoComplete="off" />
          </Form.Item>
          <Form.Item
            name="idCard"
            label="身份证号码"
            rules={[{ required: true, message: "请输入身份证号码" }]}
          >
            <Input placeholder="请输入身份证号码" autoComplete="off" />
          </Form.Item>
          <Form.Item name="height" label="身高（厘米）">
            <InputNumber
              placeholder="请输入身高"
              min={1}
              max={300}
              style={{ width: "100%" }}
            />
          </Form.Item>
          <Form.Item name="education" label="学历">
            <Select
              placeholder="请选择学历"
              allowClear
              options={[
                { value: 0, label: "小学以下" },
                { value: 1, label: "小学" },
                { value: 2, label: "初中" },
                { value: 3, label: "中职" },
                { value: 4, label: "高中" },
                { value: 5, label: "大专" },
                { value: 6, label: "本科" },
                { value: 7, label: "研究生" },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="phone"
            label="联系电话"
            rules={[{ required: true, message: "请输入联系电话" }]}
          >
            <Input placeholder="请输入联系电话" autoComplete="off" />
          </Form.Item>
          <Form.Item
            name="address"
            label="家庭详细住址"
            rules={[{ required: true, message: "请输入家庭详细住址" }]}
          >
            <Input placeholder="请输入家庭详细住址" autoComplete="off" />
          </Form.Item>
          <Form.Item
            name="workLocation"
            label="工作地点"
            rules={[{ required: true, message: "请输入工作地点" }]}
          >
            <Input placeholder="请输入工作地点" autoComplete="off" />
          </Form.Item>
          <Form.Item
            name="certificateLevel"
            label="证书等级"
            rules={[{ required: true, message: "请选择证书等级" }]}
          >
            <Select
              placeholder="请选择证书等级"
              allowClear
              options={[
                { value: 1, label: "初级" },
                { value: 2, label: "中级" },
                { value: 3, label: "高级" },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="certificateNo"
            label="证书编号"
            rules={[{ required: true, message: "请输入证书编号" }]}
          >
            <Input placeholder="请输入证书编号" autoComplete="off" />
          </Form.Item>
          <Form.Item
            name="certificatePath"
            label="证书照片"
            rules={[{ required: true, message: "请上传证书照片" }]}
          >
            <Flex align="center" gap={12}>
              <Image
                width={100}
                height={100}
                src={certificateUrl}
                fallback={imageFallback}
              />
              <Upload
                action={fileUploadUrl}
                data={{ type: 7 }}
                accept=".jpg,.jpeg,.png"
                maxCount={1}
                listType="picture"
                showUploadList={false}
                onChange={handleCertificatePhotoChange}
              >
                <Button type="primary">上传证书照片</Button>
              </Upload>
            </Flex>
          </Form.Item>
          <Form.Item name="remark" label="备注">
            <Input.TextArea
              rows={3}
              placeholder="请输入备注"
              autoComplete="off"
            />
          </Form.Item>
          <Form.Item
            name="bizStatus"
            label="职位状态"
            rules={[{ required: true, message: "请选择职位状态" }]}
          >
            <Select
              placeholder="请选择职位状态"
              allowClear
              options={[
                { value: 0, label: "在职" },
                { value: 1, label: "离职" },
              ]}
              onChange={(value) => {
                if (value !== 1) {
                  form.setFieldValue("quitReason", undefined);
                }
              }}
            />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.bizStatus !== currentValues.bizStatus
            }
          >
            {({ getFieldValue }) =>
              getFieldValue("bizStatus") === 1 ? (
                <Form.Item
                  name="quitReason"
                  label="离职原因"
                  rules={[{ required: true, message: "请输入离职原因" }]}
                >
                  <Input.TextArea
                    rows={2}
                    placeholder="请输入离职原因"
                    autoComplete="off"
                  />
                </Form.Item>
              ) : null
            }
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default EditModal;
