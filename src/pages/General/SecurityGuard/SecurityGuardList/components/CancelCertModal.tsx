import React, { Dispatch, SetStateAction, useState } from "react";
import { Button, Form, Input, Modal, message } from "antd";
import { dataApi } from "@/apis";

interface CancelCertModalProps {
  id?: string;
  isCancelCertModalVisible: boolean;
  setIsCancelCertModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const CancelCertModal: React.FC<CancelCertModalProps> = (props) => {
  const {
    id,
    isCancelCertModalVisible,
    setIsCancelCertModalVisible,
    tableReload,
  } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    setLoading(true);
    dataApi
      .postSaveRevokeEnterpriseSecurityGuard({
        id,
        certificateRevokeReason: values.certificateRevokeReason,
      })
      .then(() => {
        message.success("操作成功");
        tableReload();
        onCancel();
      })
      .finally(() => setLoading(false));
  };

  const onCancel = () => {
    setIsCancelCertModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title="吊销证书"
      open={isCancelCertModalVisible}
      width={500}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <div style={{ marginRight: 24 }}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 100 } }}
        >
          <Form.Item
            name="certificateRevokeReason"
            label="吊销原因"
            rules={[{ required: true, message: "请输入吊销原因" }]}
          >
            <Input.TextArea
              rows={4}
              placeholder="请输入吊销原因"
              autoComplete="off"
              maxLength={500}
              showCount
            />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default CancelCertModal;
