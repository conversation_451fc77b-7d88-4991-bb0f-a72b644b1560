import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Button,
  Form,
  Input,
  Modal,
  Select,
  InputNumber,
  DatePicker,
  message,
} from "antd";
import { dataApi } from "@/apis";
import dayjs from "dayjs";

interface EditModalProps {
  currentRecord?: any;
  isEditModalVisible: boolean;
  setIsEditModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
  mode: "add" | "edit" | "view"; // 添加模式控制
  enterpriseId?: string; // 企业ID，用于添加模式
}

const EditModal: React.FC<EditModalProps> = (props) => {
  const {
    currentRecord,
    isEditModalVisible,
    setIsEditModalVisible,
    tableReload,
    mode,
    enterpriseId,
  } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (isEditModalVisible) {
      if (mode === "add") {
        // 添加模式，设置企业ID
        form.setFieldsValue({
          enterpriseId: enterpriseId,
        });
      } else if ((mode === "edit" || mode === "view") && currentRecord) {
        // 编辑或详情模式，获取详情数据
        dataApi
          .postGetEnterpriseCustomer({ id: currentRecord.id })
          .then((data) => {
            form.setFieldsValue({
              ...data,
              contractDate:
                data.contractStartDate && data.contractEndDate
                  ? [dayjs(data.contractStartDate), dayjs(data.contractEndDate)]
                  : undefined,
            });
          });
      }
    }
  }, [isEditModalVisible, mode, currentRecord, enterpriseId, form]);

  const onFinish = (values: any) => {
    setLoading(true);

    // 处理合同日期，转换为时间戳
    const contractStartDate = values.contractDate?.[0]?.valueOf();
    const contractEndDate = values.contractDate?.[1]?.valueOf();

    const submitData = {
      ...values,
      contractStartDate,
      contractEndDate,
      contractDate: undefined, // 移除临时字段
    };

    const apiCall =
      mode === "add"
        ? dataApi.postSaveEnterpriseCustomer(submitData)
        : dataApi.postEditEnterpriseCustomer(submitData);

    apiCall
      .then(() => {
        message.success("操作成功");
        tableReload();
        onCancel();
      })
      .finally(() => setLoading(false));
  };

  const onCancel = () => {
    setIsEditModalVisible(false);
    form.resetFields();
  };

  // 根据模式设置标题
  const getTitle = () => {
    switch (mode) {
      case "add":
        return "添加服务单位";
      case "edit":
        return "编辑服务单位";
      case "view":
        return "服务单位详情";
      default:
        return "服务单位";
    }
  };

  const isViewMode = mode === "view";

  return (
    <Modal
      title={getTitle()}
      open={isEditModalVisible}
      width={600}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          {isViewMode ? "关闭" : "取消"}
        </Button>,
        !isViewMode && (
          <Button
            key="confirm"
            type="primary"
            htmlType="submit"
            form="modal"
            loading={loading}
          >
            确定
          </Button>
        ),
      ]}
    >
      <div style={{ marginRight: 24 }}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 120 } }}
          disabled={isViewMode}
        >
          <Form.Item name="id" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="enterpriseId" hidden>
            <Input />
          </Form.Item>
          <Form.Item
            name="name"
            label="服务单位"
            rules={[{ required: true, message: "请输入服务单位" }]}
          >
            <Input placeholder="请输入服务单位" autoComplete="off" />
          </Form.Item>
          <Form.Item
            name="postCount"
            label="岗位人数"
            rules={[{ required: true, message: "请输入岗位人数" }]}
          >
            <InputNumber
              placeholder="请输入岗位人数"
              min={1}
              style={{ width: "100%" }}
            />
          </Form.Item>
          <Form.Item
            name="postType"
            label="岗位类别"
            rules={[{ required: true, message: "请选择岗位类别" }]}
          >
            <Select
              placeholder="请选择岗位类别"
              options={[
                { value: 0, label: "门卫" },
                { value: 1, label: "巡逻" },
                { value: 2, label: "守护" },
                { value: 3, label: "押运" },
                { value: 4, label: "随护" },
                { value: 5, label: "安检" },
                { value: 6, label: "技防" },
                { value: 7, label: "司机" },
                { value: 8, label: "自定义" },
              ]}
              onChange={(value) => {
                if (value !== 8) {
                  form.setFieldValue("postTypeCustomize", undefined);
                }
              }}
            />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.postType !== currentValues.postType
            }
          >
            {({ getFieldValue }) =>
              getFieldValue("postType") === 8 ? (
                <Form.Item
                  name="postTypeCustomize"
                  label="其他岗位"
                  rules={[{ required: true, message: "请输入其他岗位信息" }]}
                >
                  <Input placeholder="请输入其他岗位信息" autoComplete="off" />
                </Form.Item>
              ) : null
            }
          </Form.Item>
          <Form.Item
            name="contractDate"
            label="合同起止日期"
            rules={[{ required: true, message: "请选择合同起止日期" }]}
          >
            <DatePicker.RangePicker
              placeholder={["开始日期", "结束日期"]}
              style={{ width: "100%" }}
            />
          </Form.Item>
          <Form.Item
            name="annualPostAmount"
            label="年岗位金额（元）"
            rules={[{ required: true, message: "请输入年岗位金额" }]}
          >
            <InputNumber
              placeholder="请输入年岗位金额"
              min={0}
              precision={2}
              style={{ width: "100%" }}
            />
          </Form.Item>
          <Form.Item name="remark" label="备注">
            <Input.TextArea
              rows={3}
              placeholder="请输入备注"
              autoComplete="off"
            />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default EditModal;
