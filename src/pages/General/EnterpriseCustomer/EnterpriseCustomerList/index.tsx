import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import {
  Table,
  Space,
  Button,
  Flex,
  type TableColumnsType,
  type TablePaginationConfig,
  Input,
  Modal,
  message,
} from "antd";
import { dataApi } from "@/apis";
import dayjs from "dayjs";
import EditModal from "./components/EditModal";
import PermButton from "@/components/PermButton";

const EnterpriseCustomerList: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<any[]>([]);
  const [currentRecord, setCurrentRecord] = useState<any>();
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [modalMode, setModalMode] = useState<"add" | "edit" | "view">("add");
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [searchText, setSearchText] = useState("");

  const { enterpriseId } = useParams();

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetEnterpriseCustomerPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        enterpriseId: enterpriseId !== "all" ? enterpriseId : null,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleView = (record: any) => {
    setCurrentRecord(record);
    setModalMode("view");
    setIsEditModalVisible(true);
  };

  const handleAdd = () => {
    setCurrentRecord(null);
    setModalMode("add");
    setIsEditModalVisible(true);
  };

  const handleEdit = (record: any) => {
    setCurrentRecord(record);
    setModalMode("edit");
    setIsEditModalVisible(true);
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: "确认删除",
      content: `确定要删除服务"${record.name}"吗？`,
      onOk: () =>
        dataApi.postDelEnterpriseCustomer({ id: record.id }).then(() => {
          message.success("删除成功");
          getDataSourceRequest();
        }),
    });
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "注册单位",
      dataIndex: "enterpriseName",
      render: (value) => value ?? "-",
    },
    {
      title: "服务单位",
      dataIndex: "name",
      minWidth: 90,
      render: (value) => value ?? "-",
    },

    {
      title: "岗位人数",
      dataIndex: "postCount",
      minWidth: 90,
      render: (value) => value ?? "-",
    },
    {
      title: "年岗位金额（年）",
      dataIndex: "annualPostAmount",
      minWidth: 132,
      render: (value) => value ?? "-",
    },
    {
      title: "操作时间",
      dataIndex: "operatorTime",
      minWidth: 90,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="small">
          <PermButton
            types={[1]}
            roles={[]}
            type="link"
            size="small"
            onClick={() => handleEdit(record)}
          >
            修改
          </PermButton>
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            onClick={() => handleView(record)}
          >
            详情
          </PermButton>
          <PermButton
            types={[1]}
            roles={[]}
            type="link"
            size="small"
            onClick={() => handleDelete(record)}
          >
            删除
          </PermButton>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Flex vertical gap={12}>
        <Space>
          <Input
            placeholder="请输入服务单位"
            style={{ width: 200 }}
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          {enterpriseId !== "all" && (
            <PermButton
              types={[1]}
              roles={[]}
              type="primary"
              onClick={handleAdd}
            >
              添加
            </PermButton>
          )}
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <EditModal
        currentRecord={currentRecord}
        isEditModalVisible={isEditModalVisible}
        setIsEditModalVisible={setIsEditModalVisible}
        tableReload={getDataSourceRequest}
        mode={modalMode}
        enterpriseId={enterpriseId}
      />
    </div>
  );
};

export default EnterpriseCustomerList;
