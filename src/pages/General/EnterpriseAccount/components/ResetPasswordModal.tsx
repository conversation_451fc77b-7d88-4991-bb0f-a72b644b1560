import React, { Dispatch, SetStateAction, useState } from "react";
import { Button, Form, Input, Modal, message } from "antd";
import { dataApi } from "@/apis";

interface ResetPasswordModalProps {
  currentRecord?: any;
  isResetPasswordModalVisible: boolean;
  setIsResetPasswordModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const ResetPasswordModal: React.FC<ResetPasswordModalProps> = (props) => {
  const {
    currentRecord,
    isResetPasswordModalVisible,
    setIsResetPasswordModalVisible,
    tableReload,
  } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    setLoading(true);
    dataApi
      .postResetEnterpriseAccountPwd({
        id: currentRecord?.id,
        password: values.password,
      })
      .then(() => {
        message.success("操作成功");
        tableReload();
        onCancel();
      })
      .finally(() => setLoading(false));
  };

  const onCancel = () => {
    setIsResetPasswordModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title="重置密码"
      open={isResetPasswordModalVisible}
      width={400}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <div style={{ marginRight: 24 }}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 100 } }}
        >
          <Form.Item
            name="password"
            label="新密码"
            rules={[{ required: true, message: "请输入新密码" }]}
          >
            <Input.Password placeholder="请输入新密码" autoComplete="off" />
          </Form.Item>
          <Form.Item
            label="确认密码"
            name="password2"
            dependencies={["password"]}
            rules={[
              {
                required: true,
                message: "请输入确认密码",
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("password") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error("新密码与确认密码不一致"));
                },
              }),
            ]}
          >
            <Input.Password placeholder="请输入确认密码" autoComplete="off" />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default ResetPasswordModal;
