import React, { useEffect, useRef, useState } from "react";
import { Button, ButtonProps, Flex, Radio, RadioProps } from "antd";
import { useReactToPrint } from "react-to-print";
import "./style.less";
import { dataApi } from "@/apis";
import dayjs from "dayjs";

interface IPrintCertificateButtonProps {
  id?: string;
  afterPrint?: () => void;
}

const PrintCertificateButton: React.FC<
  IPrintCertificateButtonProps & ButtonProps
> = (props) => {
  const { children, afterPrint, id, ...restProps } = props;
  const contentRef = useRef<HTMLDivElement>(null);
  const reactToPrintFn = useReactToPrint({
    contentRef,
    onAfterPrint: afterPrint,
    pageStyle: `
      @page {
        size: A4 portrait !important;
        margin: 0;
      }
      @media print {
        html, body {
          height: initial !important;
          overflow: initial !important;
          -webkit-print-color-adjust: exact;
          font-family: "SimSun", "FangSong" !important;
        }
      }
    `,
  });
  const [certificate, setCertificate] = useState<any>();

  const handleButtonClick = () => {
    if (!id) return;
    dataApi.postGetPrintExamUserCertificate(id).then((data) => {
      setCertificate(data);
      setTimeout(() => {
        reactToPrintFn();
      }, 100);
    });
  };

  return (
    <div className="print-certificate-button">
      <Button {...restProps} onClick={() => handleButtonClick()}>
        {children}
      </Button>
      <div className="print-certificate-wrapper" ref={contentRef}>
        <div className="a4-page">
          <div className="certificate">
            <div className="certificate-left">
              <div className="item-result">
                <span className="result-name white-border">
                  {certificate?.examUserName}
                </span>
                <span className="result-desc white-color">
                  {/* 经国家保安 */}
                </span>
              </div>
              <div className="result-text white-color">
                {/* 员考试审查合格。 */}
              </div>
              <div className="item-action white-color">{/* 特颁此证。 */}</div>
              <div className="item-level ">{certificate?.certificateLevel}</div>
              <div className="item-gov white-color">
                {/* 发证公安机关（印章） */}
              </div>
              <div className="item-date">
                <span className="item-date-time white-color">{/* 发证日期: */}</span>
                <span className="item-date-year">
                  {dayjs(certificate?.certificateDate, "YYYY MM").format(
                    "YYYY"
                  )}
                </span>
                <span className="item-date-nian white-color">
                  {/* 年 */}

                </span>
                <span className="item-date-month">
                  {dayjs(certificate?.certificateDate, "YYYY MM").format("MM")}
                </span>
                <span className="white-color">{/* 月 */}</span>
              </div>
              <div className="item-no">
                <span className="item-no-text white-color">
                  {/* 证书编号: */}
                </span>
                {/* <span className="item-no-province white-color">吉</span> */}
                <span className="item-no-value white-border">
                  {certificate?.certificateNo}
                </span>
              </div>
            </div>
            <div className="certificate-right">
              <div className="user-photo">
                {/* <img src={certificate?.examUserPhotoUrl} alt="" /> */}
              </div>

              <div className="user-name user-item">
                <span className="name-label label white-color">
                  {/* 姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名： */}
                </span>
                <span className="name-value value white-border">
                  {certificate?.examUserName}
                </span>
              </div>
              <div className="user-born user-item">
                <span className="born-label label white-color">
                  {/* 出生年月： */}
                </span>
                <span className="born-value value white-border">
                  {dayjs(certificate?.examUserBirthday, "YYYY MM DD").format(
                    "YYYY年MM月DD日"
                  )}
                </span>
              </div>
              <div className="user-adress user-item">
                <span className="adress-label label white-color">
                  {/* 住&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;址： */}
                </span>
                <span className="adress-value value">
                  {certificate?.examUserAddress}
                </span>
              </div>

              <div className="user-card user-item">
                <span className="card-label label white-color">
                  {/* 身份证号： */}
                </span>
                <span className="card-name value white-border">
                  {certificate?.examUserIdCard}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PrintCertificateButton;
