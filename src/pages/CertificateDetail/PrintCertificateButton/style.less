@media print {
  .print-certificate-wrapper {
    font-family: "<PERSON>m<PERSON><PERSON>", "Fang<PERSON>ong" !important; // 添加打印字体设置
    // position: fixed;
    // z-index: -1000;
    // opacity: 0;
    // background-color: lightblue;
    overflow: hidden;
    box-sizing: border-box;

    .a4-page {
      width: 210mm;
      height: 297mm;
      padding: 0;
      overflow: hidden;
      box-sizing: border-box;

      // background: lightblue;
      &:not(:last-child) {
        page-break-after: always;
      }
      &:last-child {
        page-break-after: avoid;
      }
      .certificate {
        width: 100%;
        height: 100%;
        display: flex;
        overflow: hidden;
        box-sizing: border-box;
        width: 166mm;
        height: 125mm;
        border-radius: 6mm;
        position: relative;
        top: -10mm;
        left: -5mm;
        // border: 0.5mm solid red;

        .certificate-left {
          // background: lightgray;
          flex: 0 0 83mm;
          padding-top: 24mm;
          overflow: hidden;

          .item-result {
            font-weight: 300;
            font-size: 4mm;
            color: #000;
            line-height: 4mm;
            display: flex;
            height: 4mm;
            padding-left: 17mm;
            margin-bottom: 8mm;
            .result-name {
              width: 30mm;
              display: block;
              border-bottom: 0.5mm solid #030000;
              text-align: center;
              height: 4mm;
              line-height: 4mm;
              letter-spacing: 1.1mm;
            }
            .result-desc {
              padding-left: 1.1mm;
              height: 4mm;
              line-height: 4mm;
              letter-spacing: 1.1mm;
              // width: 2mm;
              text-align: center;
            }
          }
          .result-text {
            display: block;
            text-align: left;
            font-weight: 300;
            font-size: 4mm;
            color: #000;
            height: 4mm;
            line-height: 4mm;
            margin-left: 8mm;
            margin-bottom: 8mm;
            letter-spacing: 1.1mm;
          }
          .item-action {
            margin-left: 17mm;
            font-weight: 300;
            font-size: 4mm;
            color: #000;
            height: 4mm;
            line-height: 4mm;
            letter-spacing: 1.1mm;
            margin-bottom: 10mm;
          }

          .item-level {
            text-align: center;
            height: 4mm;
            line-height: 4mm;
            letter-spacing: 1.1mm;
            margin-bottom: 10mm;
          }

          .item-gov {
            font-weight: 300;
            font-size: 3.5mm;
            color: #000;
            padding-left: 31mm;
            letter-spacing: 0.2mm;
            height: 4mm;
            line-height: 4mm;
            margin-bottom: 9mm;
          }

          .item-date {
            font-weight: 300;
            font-size: 3.5mm;
            color: #000;
            padding-left: 31mm;

            margin-bottom: 11mm;
            height: 4mm;
            line-height: 4mm;
            .item-date-time {
              display: inline-block;
              width: 18mm;
            }
            .item-date-year {
              text-align: center;
              display: inline-block;
              width: 12mm;
              height: 4mm;
              line-height: 4mm;
            }
            .item-date-nian {
              display: inline-block;
              width: 4mm;
            }
            .item-date-month {
              text-align: center;
              display: inline-block;
              width: 8mm;
              height: 4mm;
              line-height: 4mm;
            }
          }

          .item-no {
            text-align: left;
            font-weight: 400;
            padding-left: 8mm;
            display: flex;
            height: 5mm;
            line-height: 5mm;
            .item-no-text {
              display: block;
              height: 5mm;
              font-weight: 300;
              font-size: 4mm;
              color: #000;
              letter-spacing: 1.1mm;
              width: 32mm;
            }
            .item-no-province {
              display: block;
              height: 5mm;
              font-weight: 300;
              font-size: 4mm;
              color: #000;
              padding-left: 2mm;
            }
            .item-no-value {
              display: block;
              height: 5mm;
              line-height: 5mm;
              font-weight: 300;
              font-size: 4mm;
              color: #000;
              border-bottom: 0.5mm solid #030000;
              width: 36mm;
              text-align: center;
            }
          }
        }
        .certificate-right {
          // background: lightgreen;
          flex: 0 0 83mm;
          padding-top: 16mm;
          padding-left: 12mm;
          padding-right: 8mm;
          overflow: hidden;
          box-sizing: border-box;

          .user-photo {
            width: 100%;
            margin-left: 17mm;
            margin-bottom: 5mm;
            border: 0.5mm solid white;
            width: 27mm;
            height: 35.5mm;
            img {
              object-fit: cover;
            }
          }
          .user-item {
            overflow: hidden;
            display: flex;
            height: 9mm;
            line-height: 11mm;
            .label {
              width: 18mm;
              height: 9mm;
              display: inline-block;
              font-weight: 300;
              font-size: 3.5mm;
              color: #000000;
              overflow: hidden;
              display: flex;
            }
            .value {
              width: 45mm;
              // background: white;
              height: 9mm;
              overflow: hidden;
              display: inline-block;
              width: 45mm;
              font-size: 4mm;
              font-weight: 400;
              border-bottom: 0.5mm solid #030000;
              // letter-spacing: 1.1mm;
            }
          }
          .user-adress {
            height: 27mm;
            .adress-label {
              display: flex;
              align-items: start;
            }
            .adress-value {
              padding-top: 1mm;
              height: 27mm;
              border-bottom: none;
              line-height: 9mm;
              // letter-spacing: 1.1mm;

            }
          }
        }
      }
    }
  }
  .white-color {
    color: white !important;
  }
  .white-border {
    border-bottom: 1px solid white !important;
    border-bottom: none !important;
  }
}
.print-certificate-button {
  .print-certificate-wrapper {
    font-family: "SimSun", "FangSong" !important; // 添加打印字体设置
    position: fixed;
    z-index: -1000;
    opacity: 0;
    // background-color: lightblue;
    overflow: hidden;
    box-sizing: border-box;

    .a4-page {
      width: 210mm;
      height: 297mm;
      padding: 0;
      overflow: hidden;
      box-sizing: border-box;

      // background: lightblue;
      &:not(:last-child) {
        page-break-after: always;
      }
      &:last-child {
        page-break-after: avoid;
      }
      .certificate {
        width: 100%;
        height: 100%;
        display: flex;
        overflow: hidden;
        box-sizing: border-box;
        width: 166mm;
        height: 125mm;
        border-radius: 6mm;
        position: relative;
        top: -10mm;
        left: -5mm;
        // border: 0.5mm solid red;

        .certificate-left {
          // background: lightgray;
          flex: 0 0 83mm;
          padding-top: 24mm;
          overflow: hidden;

          .item-result {
            font-weight: 300;
            font-size: 4mm;
            color: #000;
            line-height: 4mm;
            display: flex;
            height: 4mm;
            padding-left: 17mm;
            margin-bottom: 8mm;
            .result-name {
              width: 30mm;
              display: block;
              border-bottom: 0.5mm solid #030000;
              text-align: center;
              height: 4mm;
              line-height: 4mm;
              letter-spacing: 1.1mm;
            }
            .result-desc {
              padding-left: 1.1mm;
              height: 4mm;
              line-height: 4mm;
              letter-spacing: 1.1mm;
              // width: 2mm;
              text-align: center;
            }
          }
          .result-text {
            display: block;
            text-align: left;
            font-weight: 300;
            font-size: 4mm;
            color: #000;
            height: 4mm;
            line-height: 4mm;
            margin-left: 8mm;
            margin-bottom: 8mm;
            letter-spacing: 1.1mm;
          }
          .item-action {
            margin-left: 17mm;
            font-weight: 300;
            font-size: 4mm;
            color: #000;
            height: 4mm;
            line-height: 4mm;
            letter-spacing: 1.1mm;
            margin-bottom: 10mm;
          }

          .item-level {
            text-align: center;
            height: 4mm;
            line-height: 4mm;
            letter-spacing: 1.1mm;
            margin-bottom: 10mm;
          }

          .item-gov {
            font-weight: 300;
            font-size: 3.5mm;
            color: #000;
            padding-left: 31mm;
            letter-spacing: 0.2mm;
            height: 4mm;
            line-height: 4mm;
            margin-bottom: 9mm;
          }

          .item-date {
            font-weight: 300;
            font-size: 3.5mm;
            color: #000;
            padding-left: 31mm;

            margin-bottom: 11mm;
            height: 4mm;
            line-height: 4mm;
            .item-date-time {
              display: inline-block;
              width: 18mm;
            }
            .item-date-year {
              text-align: center;
              display: inline-block;
              width: 12mm;
              height: 4mm;
              line-height: 4mm;
            }
            .item-date-nian {
              display: inline-block;
              width: 4mm;
            }
            .item-date-month {
              text-align: center;
              display: inline-block;
              width: 8mm;
              height: 4mm;
              line-height: 4mm;
            }
          }

          .item-no {
            text-align: left;
            font-weight: 400;
            padding-left: 8mm;
            display: flex;
            height: 5mm;
            line-height: 5mm;
            .item-no-text {
              display: block;
              height: 5mm;
              font-weight: 300;
              font-size: 4mm;
              color: #000;
              letter-spacing: 1.1mm;
              width: 32mm;
            }
            .item-no-province {
              display: block;
              height: 5mm;
              font-weight: 300;
              font-size: 4mm;
              color: #000;
              padding-left: 2mm;
            }
            .item-no-value {
              display: block;
              height: 5mm;
              line-height: 5mm;
              font-weight: 300;
              font-size: 4mm;
              color: #000;
              border-bottom: 0.5mm solid #030000;
              width: 36mm;
              text-align: center;
            }
          }
        }
        .certificate-right {
          // background: lightgreen;
          flex: 0 0 83mm;
          padding-top: 16mm;
          padding-left: 12mm;
          padding-right: 8mm;
          overflow: hidden;
          box-sizing: border-box;

          .user-photo {
            width: 100%;
            margin-left: 17mm;
            margin-bottom: 5mm;
            border: 0.5mm solid white;
            width: 27mm;
            height: 35.5mm;
            img {
              object-fit: cover;
            }
          }
          .user-item {
            overflow: hidden;
            display: flex;
            height: 9mm;
            line-height: 11mm;
            .label {
              width: 18mm;
              height: 9mm;
              display: inline-block;
              font-weight: 300;
              font-size: 3.5mm;
              color: #000000;
              overflow: hidden;
              display: flex;
            }
            .value {
              width: 45mm;
              // background: white;
              height: 9mm;
              overflow: hidden;
              display: inline-block;
              width: 45mm;
              font-size: 3.5mm;
              font-weight: 400;
              border-bottom: 0.5mm solid #030000;
            }
          }
          .user-adress {
            height: 27mm;
            .adress-label {
              display: flex;
              align-items: start;
            }
            .adress-value {
              padding-top: 1mm;
              height: 27mm;
              border-bottom: none;
              line-height: 9mm;
            }
          }
        }
      }
    }
  }
  .white-color {
    color: white !important;
  }
  .white-border {
    border-bottom: 1px solid white !important;
    border-bottom: none !important;
  }
}
