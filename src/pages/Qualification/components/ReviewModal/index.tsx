import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Button, Form, Input, Modal, Select, Image, message } from "antd";
import { dataApi } from "@/apis";
import { IExamUser } from "@/apis/data.model";
import { imageFallback } from "@/config";

interface ReviewModalProps {
  id?: string;
  isReviewModalVisible: boolean;
  setIsReviewModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const ReviewModal: React.FC<ReviewModalProps> = (props) => {
  const { id, isReviewModalVisible, setIsReviewModalVisible, tableReload } =
    props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [data, setData] = useState<IExamUser>();

  useEffect(() => {
    if (isReviewModalVisible && id) {
      dataApi.postGetReviewExamUser(id).then(setData);
    } else {
      setData(null);
    }
  }, [isReviewModalVisible]);

  const onFinish = (values: any) => {
    setLoading(true);
    dataApi
      .postSaveReviewExamUser({ ...values, ids: [id] })
      .then(() => {
        message.success("操作成功");
        tableReload();
        onCancel();
      })
      .finally(() => setLoading(false));
  };

  const onCancel = () => {
    setIsReviewModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title="资格审查"
      open={isReviewModalVisible}
      width={400}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          保存
        </Button>,
      ]}
    >
      <Form
        preserve={false}
        form={form}
        onFinish={onFinish}
        name="modal"
        labelAlign="right"
        labelCol={{ style: { width: 120 } }}
      >
        <Form.Item label="姓名" required>
          {data?.name}
        </Form.Item>
        <Form.Item label="联系电话" required>
          {data?.phone}
        </Form.Item>
        <Form.Item label="身份证号码" required>
          {data?.idCard}
        </Form.Item>
        <Form.Item label="照片" required>
          <Image
            width={100}
            height={140}
            src={data?.photoUrl}
            fallback={imageFallback}
          />
        </Form.Item>
        <Form.Item
          name="reviewStatus"
          label="审查状态"
          rules={[{ required: true, message: "请选择审查状态" }]}
        >
          <Select
            placeholder="请选择审查状态"
            options={[
              { value: 0, label: "审查通过" },
              { value: 1, label: "审查未通过" },
            ]}
          />
        </Form.Item>

        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.reviewStatus !== curValues.reviewStatus
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("reviewStatus") === 1 ? (
              <Form.Item
                name="reviewContent"
                label="审查未通过理由"
                rules={[{ required: true }]}
              >
                <Input.TextArea rows={2} placeholder="请输入审查未通过理由" />
              </Form.Item>
            ) : null
          }
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ReviewModal;
