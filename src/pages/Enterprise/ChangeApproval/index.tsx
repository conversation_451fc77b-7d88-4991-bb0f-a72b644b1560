import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  Space,
  Button,
  Flex,
  Select,
  type TableColumnsType,
  type TablePaginationConfig,
  Input,
} from "antd";
import { dataApi } from "@/apis";
import dayjs from "dayjs";
import ApprovalModal from "../components/ApprovalModal";

const ChangeApproval: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<any[]>([]);
  const [currentRecord, setCurrentRecord] = useState<any>();
  const [isApprovalModalVisible, setIsApprovalModalVisible] = useState(false);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [changeType, setChangeType] = useState<number | undefined>(undefined);
  const [bizStatus, setBizStatus] = useState<number | undefined>(undefined);
  const [searchText, setSearchText] = useState("");

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetChangeApprovalPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        changeType,
        bizStatus,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleApprovalFlow = () => {
    navigate(`/enterprise/setupApproval/approvalFlow?type=1`);
  };

  const handleApproval = (record: any) => {
    setCurrentRecord(record);
    setIsApprovalModalVisible(true);
  };

  const handleEnterpriseDetail = (record: any) => {
    navigate(`/enterprise/changeApproval/enterpriseDetail/${record.id}`);
  };

  const handleApprovalDetail = (record: any) => {
    navigate(`/enterprise/changeApproval/detail/${record.id}`);
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "注册单位",
      dataIndex: "name",
      render: (value) => value ?? "-",
    },
    {
      title: "统一社会信用代码",
      dataIndex: "creditCode",
      minWidth: 132,
      render: (value) => value ?? "-",
    },
    {
      title: "联系人",
      dataIndex: "contactName",
      minWidth: 80,
      render: (value) => value ?? "-",
    },
    {
      title: "保安服务类型",
      dataIndex: "serviceType",
      minWidth: 120,
      render: (value) =>
        ({
          0: "保安培训单位",
          1: "保安服务公司（保安服务分公司）",
          2: "武装守护押运",
          3: "公司自行招用保安员的单位",
          4: "物业",
          5: "跨区域保安服务公司",
        })[value],
    },
    {
      title: "变更申请类型",
      dataIndex: "changeType",
      minWidth: 120,
      render: (value) =>
        ({
          0: "法人信息变更",
          1: "股东信息变更",
        })[value],
    },
    {
      title: "提交时间",
      dataIndex: "applyTime",
      minWidth: 90,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "审查状态",
      dataIndex: "bizStatus",
      minWidth: 90,
      render: (value) =>
        ({
          0: "初始化状态（注册）",
          1: "未提交（修改中）",
          2: "撤回修改",
          3: "待审核（已提交）",
          4: "审核通过",
          5: "审核不通过",
        })[value],
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            key="approval"
            onClick={() => handleApproval(record)}
          >
            审查
          </Button>
          <Button
            type="link"
            size="small"
            key="enterpriseDetail"
            onClick={() => handleEnterpriseDetail(record)}
          >
            公司详情
          </Button>
          <Button
            type="link"
            size="small"
            key="approvalDetail"
            onClick={() => handleApprovalDetail(record)}
          >
            审查详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Flex vertical gap={12}>
        <Space>
          <Select
            placeholder="请选择审查状态"
            style={{ width: 200 }}
            allowClear
            value={bizStatus}
            onChange={(val) => {
              setBizStatus(val);
              handleSearch();
            }}
            onSelect={handleSearch}
            onClear={() => {
              setBizStatus(undefined);
              handleSearch();
            }}
            options={[
              { value: "", label: "所有状态" },
              { value: 0, label: "待审核" },
              { value: 1, label: "审核通过" },
              { value: 2, label: "审核不通过" },
            ]}
          />
          <Select
            placeholder="请选择变更类型"
            style={{ width: 200 }}
            allowClear
            value={changeType}
            onChange={(data) => {
              setChangeType(data);
              handleSearch();
            }}
            onSelect={handleSearch}
            onClear={() => {
              setChangeType(undefined);
              handleSearch();
            }}
            options={[
              { value: "", label: "所有类型" },
              { value: 0, label: "法人信息变更" },
              { value: 1, label: "股东信息变更" },
            ]}
          />
          <Input
            placeholder="请输入企业名称或统一社会信用代码"
            style={{ width: 280 }}
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <Button type="primary" onClick={handleApprovalFlow}>
            审批流程
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <ApprovalModal
        id={currentRecord?.id}
        type="change"
        isApprovalModalVisible={isApprovalModalVisible}
        setIsApprovalModalVisible={setIsApprovalModalVisible}
        tableReload={getDataSourceRequest}
      ></ApprovalModal>
    </div>
  );
};

export default ChangeApproval;
