import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import { Space, Button, Spin, Card, Flex, message } from "antd";
import { dataApi } from "@/apis";
import LegalRepresentativeForm from "../components/LegalRepresentativeForm";
import ShareholdersForm from "../components/ShareholdersForm";
import ApprovalModal from "../components/ApprovalModal";

const SetupEnterpriseDetail: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>(null);
  const [isApprovalModalVisible, setIsApprovalModalVisible] = useState(false);

  const { id } = useParams();

  const getDataSourceRequest = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const data = await dataApi.postGetFullChangeApplication(id);
      if (data) {
        setData(data);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getDataSourceRequest();
  }, [id]);

  const handleApproval = () => {
    setIsApprovalModalVisible(true);
  };

  const handlePrint = () => {
    // TODO 打印
    message.error("暂未实现该功能");
  };

  return (
    <div style={{ minWidth: "1280px", overflowX: "auto" }}>
      <Flex vertical gap={12}>
        <Space>
          <Button
            type="primary"
            onClick={handleApproval}
            disabled={data?.approveFlag !== 1}
          >
            审查
          </Button>
          <Button type="primary" onClick={handlePrint}>
            打印
          </Button>
        </Space>
        <Card size="small">
          <div style={{ height: "calc(100vh - 210px)", overflow: "auto" }}>
            <Spin spinning={loading}>
              {data?.changeType === 0 ? (
                <LegalRepresentativeForm
                  initialValues={data?.legalRepresentative}
                  disabled
                />
              ) : (
                <ShareholdersForm initialValues={data?.shareholders} disabled />
              )}
            </Spin>
          </div>
        </Card>
      </Flex>
      <ApprovalModal
        id={id}
        type="change"
        isApprovalModalVisible={isApprovalModalVisible}
        setIsApprovalModalVisible={setIsApprovalModalVisible}
        tableReload={getDataSourceRequest}
      ></ApprovalModal>
    </div>
  );
};

export default SetupEnterpriseDetail;
