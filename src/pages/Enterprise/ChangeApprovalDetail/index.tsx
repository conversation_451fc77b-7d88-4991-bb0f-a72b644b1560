import React, { useEffect, useState, useRef } from "react";
import { useParams, useNavigate } from "react-router";
import {
  Space,
  Button,
  message,
  Card,
  Select,
  Form,
  ConfigProvider,
  Spin,
} from "antd";
import { dataApi } from "@/apis";
import LegalRepresentativeForm, {
  LegalRepresentativeFormMethods,
} from "../components/LegalRepresentativeForm";
import ShareholdersForm, {
  ShareholdersFormMethods,
} from "../components/ShareholdersForm";
import ApprovalProgress from "../components/ApprovalProgress";

const ChangeApprovalDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>(null);
  const [saveDraftLoading, setSaveDraftLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [changeType, setChangeType] = useState<number>(0);

  // Form refs
  const legalRepresentativeFormRef =
    useRef<LegalRepresentativeFormMethods>(null);
  const shareholdersFormRef = useRef<ShareholdersFormMethods>(null);

  const isApprovalProgressing = [2, 3, 4].includes(data?.bizStatus);
  const contentHeight = isApprovalProgressing
    ? "calc(100vh - 340px)"
    : "calc(100vh - 225px)";

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    if (id === "new") {
      // 新建模式，设置默认数据
      setData({ bizStatus: 0 });
      handleChangeType(0);
      return;
    } else {
      setLoading(true);
      try {
        const result = await dataApi.postGetFullChangeApplication(id);
        if (result) {
          setChangeType(result.changeType);
          setData(result);
        }
      } finally {
        setLoading(false);
      }
    }
  };

  // 加载初始数据（用于新建变更申请）
  const loadInitialData = async (changeType: number) => {
    setLoading(true);
    try {
      if (changeType === 0) {
        // 获取初始法人信息
        const legalRepresentative =
          await dataApi.postGetInitLegalRepresentative();
        setData({
          chatType: 0,
          legalRepresentative,
        });
      } else if (changeType === 1) {
        // 获取初始股东信息
        const shareholders = await dataApi.postGetInitShareholders();
        setData({
          chatType: 1,
          shareholders,
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleChangeType = (value) => {
    setChangeType(value);
    loadInitialData(value);
  };

  // 收集表单数据
  const collectFormData = () => {
    const formData: any = {
      id: data.id,
      changeType,
    };

    if (changeType === 0) {
      // 法人信息变更
      const legalRepresentativeData =
        legalRepresentativeFormRef.current?.getValues();
      if (legalRepresentativeData) {
        formData.legalRepresentative = legalRepresentativeData;
      }
    } else if (changeType === 1) {
      // 股东信息变更
      const shareholdersData = shareholdersFormRef.current?.getValues();
      if (shareholdersData) {
        formData.shareholders = shareholdersData;
      }
    }

    return formData;
  };

  // 保存草稿
  const handleSaveDraft = async (showMessage = true) => {
    if (saveDraftLoading) return;

    setSaveDraftLoading(true);
    try {
      const formData = collectFormData();
      const result = await dataApi.saveChangeApplicationDraft(formData);

      // 如果是新建模式，保存成功后设置ID
      if (id === "new" && result) {
        setData((prev) => {
          const next = { ...prev };
          next.id = result;
          return next;
        });
      }
      if (showMessage) {
        message.success("草稿保存成功");
      }
    } finally {
      setSaveDraftLoading(false);
    }
  };

  // 验证表单
  const validateForms = async () => {
    try {
      if (changeType === 0) {
        // 验证法人信息
        await legalRepresentativeFormRef.current?.validateFields();
      } else if (changeType === 1) {
        // 验证股东信息
        await shareholdersFormRef.current?.validateFields();
      }
      return true;
    } catch {
      return false;
    }
  };

  // 处理后端验证错误
  const handleValidationErrors = (errorData: any[]) => {
    errorData.forEach((errorGroup) => {
      const { key, items } = errorGroup;
      if (key === "legalRepresentative") {
        Object.values(items).forEach((errors: any) => {
          legalRepresentativeFormRef.current?.setFieldsError(errors);
        });
      } else if (key === "shareholders") {
        Object.entries(items).forEach(([id, errors]: [string, any]) => {
          const errorsWithId = errors.map((error: any) => ({
            ...error,
            id,
          }));
          shareholdersFormRef.current?.setFieldsError(errorsWithId);
        });
      }
    });
  };

  // 提交审核
  const handleSubmit = async () => {
    if (submitLoading) return;

    setSubmitLoading(true);

    try {
      // 1. 先保存草稿
      await handleSaveDraft(false);

      // 2. 前端验证
      const isValid = await validateForms();
      if (!isValid) {
        message.error("请完善内容，检查标红的字段");
        return;
      }

      // 3. 提交审核（只有在编辑模式下才能提交）
      if (data?.id) {
        await dataApi.submitChangeApplication({ id: data.id });
        message.success("提交审核成功");
      } else {
        message.error("请先保存草稿后再提交");
        return;
      }

      // 4. 重定向到编辑模式
      navigate(`/enterprise/changeApply/detail/${data.id}`, {
        replace: true,
      });
    } catch (error: any) {
      // 处理后端验证错误
      if (error?.data && Array.isArray(error.data)) {
        handleValidationErrors(error.data);
        message.error("请完善内容，检查标红的字段");
      } else {
        message.error("提交失败，请重试");
      }
    } finally {
      setSubmitLoading(false);
    }
  };

  // 渲染表单内容
  const renderFormContent = () => {
    if (changeType === 0) {
      return (
        <LegalRepresentativeForm
          ref={legalRepresentativeFormRef}
          initialValues={data?.legalRepresentative}
          disabled={isApprovalProgressing}
        />
      );
    } else if (changeType === 1) {
      return (
        <ShareholdersForm
          ref={shareholdersFormRef}
          initialValues={data?.shareholders}
          disabled={isApprovalProgressing}
        />
      );
    }
    return null;
  };

  return (
    <div
      className="change-approval-detail"
      style={{ minWidth: "1280px", overflowX: "auto" }}
    >
      {/* 审批进度 */}
      {isApprovalProgressing && (
        <Card loading={loading} size="small" style={{ marginBottom: 16 }}>
          <ApprovalProgress approvals={data?.approvals} />
        </Card>
      )}

      <Card
        size="small"
        title={
          <ConfigProvider
            theme={{
              components: {
                Form: {
                  itemMarginBottom: 0,
                },
              },
            }}
          >
            <Form.Item label="变更类型" required style={{ padding: 12 }}>
              <Select
                value={changeType}
                onChange={handleChangeType}
                style={{ width: 200 }}
                disabled={isApprovalProgressing || data?.id}
                options={[
                  { value: 0, label: "法人信息变更" },
                  { value: 1, label: "股东信息变更" },
                ]}
              />
            </Form.Item>
          </ConfigProvider>
        }
        extra={
          isApprovalProgressing ? null : (
            <Space>
              <Button
                type="default"
                onClick={() => handleSaveDraft(true)}
                loading={saveDraftLoading}
                disabled={loading || isApprovalProgressing}
              >
                保存草稿
              </Button>
              <Button
                type="primary"
                onClick={handleSubmit}
                loading={submitLoading}
                disabled={loading || isApprovalProgressing}
              >
                提交审核
              </Button>
            </Space>
          )
        }
      >
        <Spin spinning={loading}>
          <div style={{ height: contentHeight, overflow: "auto" }}>
            {renderFormContent()}
          </div>
        </Spin>
      </Card>
    </div>
  );
};

export default ChangeApprovalDetail;
