import React, {
  useImperativeHandle,
  forwardRef,
  useEffect,
  useState,
} from "react";
import {
  Form,
  Select,
  Input,
  Row,
  Col,
  DatePicker,
  Upload,
  Button,
  InputNumber,
  Space,
  Divider,
  Card,
} from "antd";
import { UploadOutlined } from "@ant-design/icons";
import RegionCascader from "@/components/RegionCascader";
import dayjs from "dayjs";
import { fileUploadUrl } from "@/config";

// 定义组件对外暴露的方法
export interface EnterpriseFormMethods {
  validateFields: () => Promise<void>;
  getValues: () => any;
  setFieldsValue: (values: any) => void;
  resetFields: () => void;
  setFieldsError: (errors: Array<{ name: string; msg: string }>) => void;
}

interface EnterpriseFormProps {
  initialValues?: any;
  disabled?: boolean;
}

const EnterpriseForm = forwardRef<EnterpriseFormMethods, EnterpriseFormProps>(
  ({ initialValues, disabled = false }, ref) => {
    const [form] = Form.useForm();
    const [
      enterpriseEstablishmentFilename,
      setEnterpriseEstablishmentFilename,
    ] = useState<string>();
    const [enterpriseEstablishmentUrl, setEnterpriseEstablishmentUrl] =
      useState<string>();

    // 初始化表单数据
    useEffect(() => {
      if (initialValues) {
        // 处理日期字段
        const formattedValues = { ...initialValues };
        if (formattedValues.establishmentDate) {
          formattedValues.establishmentDate = dayjs(
            formattedValues.establishmentDate
          );
        }
        if (formattedValues.businessLicenseValidityDate) {
          formattedValues.businessLicenseValidityDate = dayjs(
            formattedValues.businessLicenseValidityDate
          );
        }

        // 处理地区字段
        if (
          formattedValues.province &&
          formattedValues.city &&
          formattedValues.county
        ) {
          formattedValues.region = [
            formattedValues.province,
            formattedValues.city,
            formattedValues.county,
          ];
        }

        // 处理文件
        if (initialValues.enterpriseEstablishmentPath) {
          setEnterpriseEstablishmentFilename(
            "申请书" + getFilePathExt(initialValues.enterpriseEstablishmentPath)
          );
        }
        if (initialValues.enterpriseEstablishmentUrl) {
          setEnterpriseEstablishmentUrl(
            initialValues.enterpriseEstablishmentUrl
          );
        }

        form.setFieldsValue(formattedValues);
      }
    }, [initialValues, form]);

    // 获取文件扩展名，如 .pdf
    const getFilePathExt = (path) => {
      const start = path.lastIndexOf(".");
      return path.substring(start);
    };

    // 处理文件上传变化
    const handleFileChange = ({ file }) => {
      if (file.status == "done") {
        form.setFieldValue("enterpriseEstablishmentPath", file.response.data);
        setEnterpriseEstablishmentFilename(
          "申请书" + getFilePathExt(file.response.data)
        );
        setEnterpriseEstablishmentUrl(URL.createObjectURL(file.originFileObj));
      } else if (file.status == "removed") {
        form.setFieldValue("enterpriseEstablishmentPath", null);
        setEnterpriseEstablishmentFilename(null);
        setEnterpriseEstablishmentUrl(null);
      }
    };

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      validateFields: async () => {
        await form.validateFields();
      },
      getValues: () => {
        const values = form.getFieldsValue(true);

        // 处理地区字段
        if (values.region && values.region.length === 3) {
          values.province = values.region[0];
          values.city = values.region[1];
          values.county = values.region[2];
        }

        return values;
      },
      setFieldsValue: (values) => {
        form.setFieldsValue(values);
      },
      resetFields: () => {
        form.resetFields();
        setEnterpriseEstablishmentFilename(null);
        setEnterpriseEstablishmentUrl(null);
      },
      setFieldsError: (errors) => {
        if (!errors || errors.length === 0) {
          // 清除所有错误 - 使用Form API获取所有字段并清除错误
          const fieldsValue = form.getFieldsValue(true);
          const clearFields = Object.keys(fieldsValue).map((name) => ({
            name,
            errors: [],
          }));
          form.setFields(clearFields);
          return;
        }

        const fields = errors.map((error) => ({
          name: error.name,
          errors: [error.msg],
        }));
        form.setFields(fields);
      },
    }));

    return (
      <div>
        <Divider orientation="left">企业基本信息</Divider>
        <Form
          form={form}
          layout="horizontal"
          labelCol={{ style: { width: 140 } }}
          disabled={disabled}
        >
          <Card size="small">
            <Row gutter={[16, 0]}>
              <Col span={8}>
                <Form.Item
                  label="保安服务类型"
                  name="serviceType"
                  rules={[{ required: false, message: "请选择保安服务类型" }]}
                >
                  <Select
                    disabled={true}
                    options={[
                      { value: 0, label: "保安培训单位" },
                      { value: 1, label: "保安服务公司（保安服务分公司）" },
                      { value: 2, label: "武装守护押运" },
                      { value: 3, label: "公司自行招用保安员的单位" },
                      { value: 4, label: "物业" },
                      { value: 5, label: "跨区域保安服务公司" },
                    ]}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="企业类别"
                  name="type"
                  rules={[{ required: true, message: "请选择企业类别" }]}
                >
                  <Select
                    options={[
                      { value: 0, label: "国有企业" },
                      { value: 1, label: "集体所有制企业" },
                      { value: 2, label: "股份制企业" },
                      { value: 3, label: "联营企业" },
                      { value: 4, label: "有限责任公司" },
                      { value: 5, label: "股份有限公司" },
                      { value: 6, label: "私营企业" },
                      { value: 7, label: "其他" },
                    ]}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="企业编码" name="code">
                  <Input placeholder="请输入企业编码" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[16, 0]}>
              <Col span={8}>
                <Form.Item label="企业所属市/县" name="region">
                  <RegionCascader placeholder="请选择省/市/县" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="管辖单位" name="supervisoryUnit">
                  <Input placeholder="请输入管辖单位" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="企业详细地址" name="address">
                  <Input placeholder="请输入企业详细地址" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[16, 0]}>
              <Col span={8}>
                <Form.Item
                  label="企业名称"
                  name="name"
                  rules={[{ required: true, message: "请输入企业名称" }]}
                >
                  <Input placeholder="请输入企业名称" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="公司类型"
                  name="legalForm"
                  rules={[{ required: true, message: "请选择公司类型" }]}
                >
                  <Select
                    placeholder="请选择公司类型"
                    options={[
                      { value: 0, label: "有限责任公司" },
                      { value: 1, label: "股份有限公司" },
                      { value: 2, label: "合伙企业" },
                      { value: 3, label: "个人独资企业" },
                      { value: 4, label: "其他" },
                    ]}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="经济类型"
                  name="economicType"
                  rules={[{ required: true, message: "请选择经济类型" }]}
                >
                  <Select
                    placeholder="请选择经济类型"
                    options={[
                      { value: 0, label: "国有经济" },
                      { value: 1, label: "集体经济" },
                      { value: 2, label: "私营经济" },
                      { value: 3, label: "个体经济" },
                      { value: 4, label: "合资经济" },
                      { value: 5, label: "股份制经济" },
                      { value: 6, label: "外商投资经济" },
                      { value: 7, label: "港澳台投资经济" },
                      { value: 8, label: "其他" },
                    ]}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[16, 0]}>
              <Col span={8}>
                <Form.Item
                  label="注册资金(元)"
                  name="registeredCapital"
                  rules={[{ required: true, message: "请输入注册资金" }]}
                >
                  <InputNumber
                    style={{ width: "100%" }}
                    placeholder="请输入注册资金"
                    min={0}
                    precision={2}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="传真" name="fax">
                  <Input placeholder="请输入传真" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="邮政编码" name="postalCode">
                  <Input placeholder="请输入邮政编码" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[16, 0]}>
              <Col span={8}>
                <Form.Item
                  label="联系人姓名"
                  name="contactName"
                  rules={[{ required: true, message: "请输入联系人姓名" }]}
                >
                  <Input placeholder="请输入联系人姓名" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="联系人电话"
                  name="contactPhone"
                  rules={[{ required: true, message: "请输入联系人电话" }]}
                >
                  <Input placeholder="请输入联系人电话" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="联系人证件号码"
                  name="contactIdCard"
                  rules={[{ required: true, message: "请输入联系人证件号码" }]}
                >
                  <Input placeholder="请输入联系人证件号码" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[16, 0]}>
              <Col span={8}>
                <Form.Item label="人力防范等级" name="humanDefenseLevel">
                  <Select
                    placeholder="请选择人力防范等级"
                    options={[
                      { value: 1, label: "一级" },
                      { value: 2, label: "二级" },
                      { value: 3, label: "三级" },
                    ]}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="武装守护押运等级" name="armedTransportLevel">
                  <Select
                    placeholder="请选择武装守护押运等级"
                    options={[
                      { value: 1, label: "一级" },
                      { value: 2, label: "二级" },
                    ]}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="安全技术防范等级" name="securityTechLevel">
                  <Select
                    placeholder="请选择安全技术防范等级"
                    options={[
                      { value: 1, label: "一级" },
                      { value: 2, label: "二级" },
                      { value: 3, label: "三级" },
                    ]}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[16, 0]}>
              <Col span={8}>
                <Form.Item label="风险评估等级" name="riskAssessmentLevel">
                  <Select
                    placeholder="请选择风险评估等级"
                    options={[
                      { value: 1, label: "一级" },
                      { value: 2, label: "二级" },
                    ]}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="客户数量" name="customerCount">
                  <InputNumber
                    style={{ width: "100%" }}
                    placeholder="请输入客户数量"
                    min={0}
                    precision={0}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="自建金库" name="selfBuildVaultFlag">
                  <Select
                    placeholder="请选择是否自建金库"
                    options={[
                      { value: 0, label: "是" },
                      { value: 1, label: "否" },
                    ]}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[16, 0]}>
              <Col span={8}>
                <Form.Item
                  label="是否建立子公司"
                  name="hasSubsidiaryFlag"
                  rules={[{ required: true, message: "请选择是否建立子公司" }]}
                >
                  <Select
                    placeholder="请选择是否建立子公司"
                    options={[
                      { value: 0, label: "是" },
                      { value: 1, label: "否" },
                    ]}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="子公司名称" name="subsidiaryName">
                  <Input placeholder="请输入子公司名称" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="营业执照经营范围" name="businessScope">
                  <Input.TextArea
                    rows={1}
                    placeholder="请输入营业执照经营范围"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[16, 0]}>
              <Col span={8}>
                <Form.Item label="成立日期" name="establishmentDate">
                  <DatePicker
                    style={{ width: "100%" }}
                    placeholder="请选择成立日期"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="营业执照截止日期"
                  name="businessLicenseValidityDate"
                >
                  <DatePicker
                    style={{ width: "100%" }}
                    placeholder="请选择营业执照截止日期"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="场地面积(平米)" name="siteArea">
                  <InputNumber
                    style={{ width: "100%" }}
                    placeholder="请输入场地面积"
                    min={0}
                    precision={2}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[16, 0]}>
              <Col span={8}>
                <Form.Item
                  label="统一社会信用代码"
                  name="creditCode"
                  rules={[
                    { required: true, message: "请输入统一社会信用代码" },
                  ]}
                >
                  <Input placeholder="请输入统一社会信用代码" />
                </Form.Item>
              </Col>
              <Col span={16}>
                <Form.Item
                  label="公司成立申请书"
                  name="enterpriseEstablishmentPath"
                >
                  <Space>
                    {enterpriseEstablishmentFilename ? (
                      <Button
                        type="link"
                        target="_blank"
                        href={enterpriseEstablishmentUrl}
                        disabled={false}
                      >
                        {enterpriseEstablishmentFilename}
                      </Button>
                    ) : (
                      <span>未上传文件</span>
                    )}
                    <Upload
                      action={fileUploadUrl}
                      data={{ type: 2 }}
                      accept=".pdf,.doc,.docx"
                      maxCount={1}
                      showUploadList={false}
                      onChange={handleFileChange}
                    >
                      <Button icon={<UploadOutlined />}>点击上传</Button>
                    </Upload>
                  </Space>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </Form>
      </div>
    );
  }
);

export default EnterpriseForm;
