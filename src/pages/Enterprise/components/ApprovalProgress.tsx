import React from "react";
import { Steps, Typography, ConfigProvider, Flex } from "antd";

const { Title, Text } = Typography;

interface ApprovalItem {
  name: string;
  label: string;
  time: string | null;
  checkContent: string | null;
}

interface ApprovalProgressProps {
  approvals?: ApprovalItem[];
}

const ApprovalProgress: React.FC<ApprovalProgressProps> = ({
  approvals = [],
}) => {
  // 构建 Steps 的 items
  const stepItems = approvals.map((approval) => {
    const time = approval.time ? <div>{approval.time}</div> : null;
    const checkContent = approval.checkContent ? (
      <Text ellipsis={{ tooltip: { title: approval.checkContent } }}>
        {approval.checkContent}
      </Text>
    ) : null;

    return {
      title: approval.name,
      subTitle: <Text>[{approval.label}]</Text>,
      description: (
        <div>
          {time}
          {checkContent}
        </div>
      ),
      status: "finish" as const,
      style: { minWidth: "max-content" },
    };
  });

  return (
    <div>
      <Flex gap={24}>
        <Title style={{ minWidth: "90px" }} level={4}>
          审批进度
        </Title>
        <div style={{ width: "calc(100% - 114px)", overflow: "auto" }}>
          <div style={{ width: stepItems.length * 220 }}>
            <ConfigProvider
              theme={{
                components: {
                  Steps: {
                    descriptionMaxWidth: 150,
                  },
                },
              }}
            >
              <Steps direction="horizontal" items={stepItems} />
            </ConfigProvider>
          </div>
        </div>
      </Flex>
    </div>
  );
};

export default ApprovalProgress;
