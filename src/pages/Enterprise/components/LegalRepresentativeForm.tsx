import React, { useImperativeHandle, forwardRef, useEffect } from "react";
import {
  Form,
  Input,
  Row,
  Col,
  DatePicker,
  Select,
  Divider,
  Flex,
  Typography,
  InputNumber,
  Card,
} from "antd";
import dayjs from "dayjs";
import RegionCascader from "@/components/RegionCascader";

const { Text } = Typography;

// 定义组件对外暴露的方法
export interface LegalRepresentativeFormMethods {
  validateFields: () => Promise<void>;
  getValues: () => any;
  setFieldsValue: (values: any) => void;
  resetFields: () => void;
  setFieldsError: (errors: Array<{ name: string; msg: string }>) => void;
}

interface LegalRepresentativeFormProps {
  initialValues?: any;
  disabled?: boolean;
}

const LegalRepresentativeForm = forwardRef<
  LegalRepresentativeFormMethods,
  LegalRepresentativeFormProps
>(({ initialValues, disabled = false }, ref) => {
  const [form] = Form.useForm();

  // 初始化表单数据
  useEffect(() => {
    if (initialValues) {
      // 处理日期字段
      const formattedValues = { ...initialValues };
      if (formattedValues.birthDate) {
        formattedValues.birthDate = dayjs(formattedValues.birthDate);
      }

      // 处理地区字段
      if (
        formattedValues.householdProvince &&
        formattedValues.householdCity &&
        formattedValues.householdCounty
      ) {
        formattedValues.householdRegion = [
          formattedValues.householdProvince,
          formattedValues.householdCity,
          formattedValues.householdCounty,
        ];
      }

      if (
        formattedValues.residenceProvince &&
        formattedValues.residenceCity &&
        formattedValues.residenceCounty
      ) {
        formattedValues.residenceRegion = [
          formattedValues.residenceProvince,
          formattedValues.residenceCity,
          formattedValues.residenceCounty,
        ];
      }

      form.setFieldsValue(formattedValues);
    }
  }, [initialValues, form]);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    validateFields: async () => {
      await form.validateFields();
    },
    getValues: () => {
      const values = form.getFieldsValue(true);

      // 处理地区字段
      if (values.householdRegion && values.householdRegion.length === 3) {
        values.householdProvince = values.householdRegion[0];
        values.householdCity = values.householdRegion[1];
        values.householdCounty = values.householdRegion[2];
      }

      if (values.residenceRegion && values.residenceRegion.length === 3) {
        values.residenceProvince = values.residenceRegion[0];
        values.residenceCity = values.residenceRegion[1];
        values.residenceCounty = values.residenceRegion[2];
      }

      return values;
    },
    setFieldsValue: (values) => {
      console.log('values', values)
      form.setFieldsValue(values);
    },
    resetFields: () => {
      form.resetFields();
    },
    setFieldsError: (errors) => {
      if (!errors || errors.length === 0) {
        // 清除所有错误 - 使用Form API获取所有字段并清除错误
        const fieldsValue = form.getFieldsValue(true);
        const clearFields = Object.keys(fieldsValue).map(name => ({
          name,
          errors: []
        }));
        form.setFields(clearFields);
        return;
      }

      const fields = errors.map(error => ({
        name: error.name,
        errors: [error.msg]
      }));
      form.setFields(fields);
    },
  }));

  return (
    <div>
      <Divider orientation="left">
        <Flex align="baseline" gap={8}>
          <span>法人代表信息</span>
          {!disabled && <Text type="danger">*以下信息均为必填项</Text>}
        </Flex>
      </Divider>
      <Form
        form={form}
        layout="horizontal"
        labelCol={{ style: { width: 140 } }}
        disabled={disabled}
      >
        <Card size="small">
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <Form.Item
                label="姓名"
                name="name"
                rules={[{ required: true, message: "请输入姓名" }]}
              >
                <Input placeholder="请输入姓名" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="公民身份号码"
                name="idCard"
                rules={[{ required: true, message: "请输入公民身份号码" }]}
              >
                <Input placeholder="请输入公民身份号码" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="性别"
                name="sex"
                rules={[{ required: true, message: "请选择性别" }]}
              >
                <Select
                  placeholder="请选择性别"
                  options={[
                    { value: 0, label: "女" },
                    { value: 1, label: "男" },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <Form.Item
                label="出生日期"
                name="birthDate"
                rules={[{ required: true, message: "请选择出生日期" }]}
              >
                <DatePicker
                  style={{ width: "100%" }}
                  placeholder="请选择出生日期"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="民族"
                name="ethnicity"
                rules={[{ required: true, message: "请输入民族" }]}
              >
                <Input placeholder="请输入民族" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="联系电话"
                name="phone"
                rules={[{ required: true, message: "请输入联系电话" }]}
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <Form.Item
                label="出资金额(元)"
                name="investmentAmount"
                rules={[{ required: true, message: "请输入出资金额" }]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="请输入出资金额"
                  precision={2}
                  min={0}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="所占股份百分比(%)"
                name="shareholdingRatio"
                rules={[{ required: true, message: "请输入所占股份百分比" }]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="请输入所占股份百分比"
                  precision={2}
                  min={0}
                  max={100}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="法人代表职务"
                name="position"
                rules={[{ required: true, message: "请输入法人代表职务" }]}
              >
                <Input placeholder="请输入法人代表职务" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <Form.Item
                label="户籍地省市县（区）"
                name="householdRegion"
                rules={[{ required: true, message: "请选择户籍地省市县" }]}
              >
                <RegionCascader placeholder="请选择省/市/县" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="户籍地详细地址"
                name="householdAddress"
                rules={[{ required: true, message: "请输入户籍地详细地址" }]}
              >
                <Input placeholder="请输入户籍地详细地址" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <Form.Item
                label="现住址省市县（区）"
                name="residenceRegion"
                rules={[{ required: true, message: "请选择现住址省市县" }]}
              >
                <RegionCascader placeholder="请选择省/市/县" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="现住址详细地址"
                name="residenceAddress"
                rules={[{ required: true, message: "请输入现住址详细地址" }]}
              >
                <Input placeholder="请输入现住址详细地址" />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Form>
    </div>
  );
});

export default LegalRepresentativeForm;
