import React, { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { Table, Space, Button, Flex, type TableColumnsType } from "antd";
import { dataApi } from "@/apis";
import { IApprovalFlow } from "@/apis/data.model";
import EditModal from "./components/EditModal";
import dayjs from "dayjs";

const ApprovalFlow: React.FC = () => {
  const [searchParams] = useSearchParams();
  const type = parseInt(searchParams.get("type") || "0"); // 0 企业设立审核，1 企业信息变更审核
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<Array<IApprovalFlow>>([]);
  const [currentRecord, setCurrentRecord] = useState<IApprovalFlow>();
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetApprovalFlowList({ type })
      .then((data) => {
        setLoading(false);
        setDataSource(data);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [type]);

  const handleAdd = () => {
    setCurrentRecord(undefined);
    setIsEditModalVisible(true);
  };

  const handleEdit = (record: IApprovalFlow) => {
    setCurrentRecord(record);
    setIsEditModalVisible(true);
  };

  const getTypeText = (type: number) => {
    return type === 0 ? "企业设立审核" : "企业信息变更审核";
  };

  const columns: TableColumnsType<IApprovalFlow> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (_, __, index) => {
        return index + 1;
      },
    },
    {
      title: "保安服务类型",
      dataIndex: "serviceTypeName",
      render: (value) => value ?? "-",
    },
    {
      title: "审批流程",
      dataIndex: "flowNode",
      render: (value) => value ?? "-",
    },
    {
      title: "修改人",
      dataIndex: "operator",
      width: 100,
      render: (value) => value ?? "-",
    },
    {
      title: "修改时间",
      dataIndex: "operatorTime",
      width: 160,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="small">
          <Button type="link" size="small" onClick={() => handleEdit(record)}>
            修改
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="approval-flow">
      <Flex vertical gap={12}>
        <Space>
          <Button type="primary" onClick={handleAdd}>
            添加审批流程
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={false}
        />
      </Flex>
      <EditModal
        type={type}
        id={currentRecord?.id}
        isEditModalVisible={isEditModalVisible}
        setIsEditModalVisible={setIsEditModalVisible}
        tableReload={getDataSourceRequest}
      />
    </div>
  );
};

export default ApprovalFlow;
