import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  Button,
  Flex,
  message,
  type TableColumnsType,
  Space,
} from "antd";
import { dataApi } from "@/apis";
import dayjs from "dayjs";

const SetupApproval: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<any[]>([]);

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetSetupApplication()
      .then((data) => {
        setLoading(false);
        seteDataSource([data]);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, []);

  const handleWithdraw = (record: any) => {
    dataApi.postWithdrawSetupApplication(record.id).then(() => {
      message.success("操作成功");
      getDataSourceRequest();
    });
  };

  const handleView = (record: any) => {
    navigate(`/enterprise/setupApply/detail/${record.id}`);
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "注册单位",
      dataIndex: "name",
      render: (value) => value ?? "-",
    },
    {
      title: "联系人",
      dataIndex: "contactName",
      minWidth: 80,
      render: (value) => value ?? "-",
    },
    {
      title: "联系人身份证号码",
      dataIndex: "contactIdCard",
      minWidth: 132,
      render: (value) => value ?? "-",
    },
    {
      title: "保安服务类型",
      dataIndex: "serviceType",
      minWidth: 120,
      render: (value) =>
        ({
          0: "保安培训单位",
          1: "保安服务公司（保安服务分公司）",
          2: "武装守护押运",
          3: "公司自行招用保安员的单位",
          4: "物业",
          5: "跨区域保安服务公司",
        })[value],
    },
    {
      title: "提交时间",
      dataIndex: "applyTime",
      minWidth: 90,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "审查状态",
      dataIndex: "bizStatus",
      minWidth: 90,
      render: (value) =>
        ({
          0: "初始化状态（注册）",
          1: "未提交（修改中）",
          2: "撤回修改",
          3: "待审核（已提交）",
          4: "审核通过",
          5: "审核不通过",
        })[value],
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            key="withdraw"
            onClick={() => handleWithdraw(record)}
          >
            撤回修改
          </Button>
          <Button
            type="link"
            size="small"
            key="view"
            onClick={() => handleView(record)}
          >
            审查详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="examination">
      <Flex vertical gap={12}>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={false}
        />
      </Flex>
    </div>
  );
};

export default SetupApproval;
