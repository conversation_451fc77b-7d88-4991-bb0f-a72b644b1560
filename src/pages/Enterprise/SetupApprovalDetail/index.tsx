import React, {
  useEffect,
  useState,
  useRef,
  useMemo,
  useCallback,
} from "react";
import { useParams } from "react-router";
import { Space, Button, message, Spin, Tabs, Card } from "antd";
import {
  LeftOutlined,
  RightOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import type { TabsProps } from "antd";
import { dataApi } from "@/apis";
import EnterpriseForm, {
  EnterpriseFormMethods,
} from "../components/EnterpriseForm";
import LegalRepresentativeForm, {
  LegalRepresentativeFormMethods,
} from "../components/LegalRepresentativeForm";
import ShareholdersForm, {
  ShareholdersFormMethods,
} from "../components/ShareholdersForm";
import ManagersForm, { ManagersFormMethods } from "../components/ManagersForm";
import MaterialsForm, {
  MaterialsFormMethods,
} from "../components/MaterialsForm";
import ApprovalProgress from "../components/ApprovalProgress";

// 类型定义
type TabKey =
  | "enterprise"
  | "legalRepresentative"
  | "shareholders"
  | "managers"
  | "materials";
type ValidationResult = { frontend: boolean; backend: boolean };
type TabValidationResults = Record<TabKey, ValidationResult>;

// 常量定义
const ICON_STYLES = {
  success: { color: "#52c41a", marginLeft: 4 },
  error: { color: "#ff4d4f", marginLeft: 4 },
} as const;

const TAB_CONFIG = [
  { key: "1", label: "公司信息", formKey: "enterprise" as TabKey },
  { key: "2", label: "法人信息", formKey: "legalRepresentative" as TabKey },
  { key: "3", label: "股东信息", formKey: "shareholders" as TabKey },
  { key: "4", label: "管理人员简历", formKey: "managers" as TabKey },
  { key: "5", label: "电子资料上传", formKey: "materials" as TabKey },
] as const;

const INITIAL_VALIDATION_STATE: TabValidationResults = {
  enterprise: { frontend: false, backend: true },
  legalRepresentative: { frontend: false, backend: true },
  shareholders: { frontend: false, backend: true },
  managers: { frontend: false, backend: true },
  materials: { frontend: false, backend: true },
};

const SetupApprovalDetail: React.FC = () => {
  // 基础状态
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>(null);
  const [activeKey, setActiveKey] = useState("1");

  // 操作状态
  const [saveDraftLoading, setSaveDraftLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);

  // 校验状态
  const [showValidationIcons, setShowValidationIcons] = useState(false);
  const [tabValidationResults, setTabValidationResults] =
    useState<TabValidationResults>(INITIAL_VALIDATION_STATE);

  // 表单引用
  const formRefs = {
    enterprise: useRef<EnterpriseFormMethods>(null),
    legalRepresentative: useRef<LegalRepresentativeFormMethods>(null),
    shareholders: useRef<ShareholdersFormMethods>(null),
    managers: useRef<ManagersFormMethods>(null),
    materials: useRef<MaterialsFormMethods>(null),
  };

  const { id } = useParams();

  // 计算属性
  const tabKeys = TAB_CONFIG.map((tab) => tab.key);
  const currentIndex = useMemo(
    () => tabKeys.indexOf(activeKey as any),
    [activeKey]
  );

  const isApprovalProgressing = [3, 4, 5].includes(data?.enterprise?.bizStatus);
  const tabContentHeight = isApprovalProgressing
    ? "calc(100vh - 350px)"
    : "calc(100vh - 250px)";

  // 获取Tab校验状态图标
  const getTabValidationIcon = useCallback(
    (tabKey: TabKey) => {
      if (!showValidationIcons) return null;

      const { frontend, backend } = tabValidationResults[tabKey];

      if (frontend && backend) {
        return <CheckCircleOutlined style={ICON_STYLES.success} />;
      }

      if (!frontend || !backend) {
        return <ExclamationCircleOutlined style={ICON_STYLES.error} />;
      }

      return null;
    },
    [showValidationIcons, tabValidationResults]
  );

  // 数据加载函数
  const loadData = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const result = await dataApi.postGetFullSetupApplication(id);
      if (result) {
        setData(result);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // 校验所有表单
  const validateAllForms =
    useCallback(async (): Promise<TabValidationResults> => {
      const results: TabValidationResults = Object.keys(
        tabValidationResults
      ).reduce((acc, key) => {
        const tabKey = key as TabKey;
        acc[tabKey] = {
          frontend: false,
          backend: tabValidationResults[tabKey].backend,
        };
        return acc;
      }, {} as TabValidationResults);

      // 并行校验所有表单
      await Promise.all(
        Object.entries(formRefs).map(async ([key, ref]) => {
          try {
            await ref.current?.validateFields();
            results[key as TabKey].frontend = true;
          } catch {
            // 校验失败，保持默认值false
          }
        })
      );

      return results;
    }, [tabValidationResults, formRefs]);

  // 收集表单数据
  const collectFormData = useCallback(() => {
    return {
      enterprise: formRefs.enterprise.current?.getValues() || {},
      legalRepresentative:
        formRefs.legalRepresentative.current?.getValues() || {},
      shareholders: formRefs.shareholders.current?.getValues() || [],
      managers: formRefs.managers.current?.getValues() || [],
      materials: formRefs.materials.current?.getValues() || [],
    };
  }, [formRefs]);

  // 添加企业ID到表单数据
  const addEnterpriseIdToFormData = (formData: any, enterpriseId: string) => {
    const result = { ...formData };

    if (result.legalRepresentative) {
      result.legalRepresentative.enterpriseId = enterpriseId;
    }

    ["shareholders", "managers", "materials"].forEach((key) => {
      if (Array.isArray(result[key])) {
        result[key].forEach((item: any) => {
          item.enterpriseId = enterpriseId;
        });
      }
    });

    return result;
  };

  // 保存草稿
  const handleSaveDraft = useCallback(
    async (showMessage = true) => {
      if (saveDraftLoading) return;

      setSaveDraftLoading(true);
      try {
        const formData = collectFormData();
        const enterpriseId = data?.enterprise?.id;

        if (!enterpriseId) {
          throw new Error("企业ID不存在");
        }

        const values = addEnterpriseIdToFormData(formData, enterpriseId);

        await dataApi.saveSetupApplicationDraft(values);
        // await loadData();

        if (showMessage) {
          message.success("草稿保存成功");
        }
      } finally {
        setSaveDraftLoading(false);
      }
    },
    [
      saveDraftLoading,
      collectFormData,
      addEnterpriseIdToFormData,
      data?.enterprise?.id,
    ]
  );

  // 清除所有表单错误
  const clearAllFormErrors = useCallback(() => {
    try {
      Object.values(formRefs).forEach((ref) => {
        ref.current?.setFieldsError([]);
      });
    } catch (error) {
      console.warn("清除表单错误时出现问题:", error);
    }
  }, [formRefs]);

  // 处理校验错误
  const handleValidationErrors = useCallback(
    (errorData: any[]) => {
      clearAllFormErrors();

      const backendValidationResults: Record<TabKey, boolean> = {
        enterprise: true,
        legalRepresentative: true,
        shareholders: true,
        managers: true,
        materials: true,
      };

      errorData.forEach((errorGroup) => {
        const { key, items } = errorGroup;
        const tabKey = key as TabKey;

        if (!formRefs[tabKey]) return;

        backendValidationResults[tabKey] = false;

        if (tabKey === "enterprise" || tabKey === "legalRepresentative") {
          Object.values(items).forEach((errors: any) => {
            formRefs[tabKey].current?.setFieldsError(errors);
          });
        } else if (tabKey === "shareholders" || tabKey === "managers") {
          Object.entries(items).forEach(([id, errors]: [string, any]) => {
            const errorsWithId = errors.map((error: any) => ({
              ...error,
              id,
            }));
            formRefs[tabKey].current?.setFieldsError(errorsWithId);
          });
        } else if (tabKey === "materials") {
          const errorsWithId = Object.entries(items).flatMap(
            ([id, errors]: [string, any]) => {
              return errors.map((error: any) => ({
                ...error,
                id,
              }));
            }
          );
          formRefs[tabKey].current?.setFieldsError(errorsWithId);
        }
      });

      // 更新后端校验状态
      setTabValidationResults((prev) => {
        const newResults = { ...prev };
        Object.keys(backendValidationResults).forEach((key) => {
          const tabKey = key as TabKey;
          newResults[tabKey] = {
            frontend: prev[tabKey].frontend,
            backend: backendValidationResults[tabKey],
          };
        });
        return newResults;
      });
    },
    [formRefs, clearAllFormErrors]
  );

  // 提交审核
  const handleSubmit = async () => {
    if (submitLoading) return; // 防止重复提交

    if (!data?.enterprise?.id) {
      message.error("企业信息不完整，无法提交");
      return;
    }

    setSubmitLoading(true);
    try {
      // 1. 先保存草稿，必须成功才能继续
      await handleSaveDraft(false);

      // 2. 校验所有表单并记录前端校验结果
      const validationResults = await validateAllForms();

      // 3. 前端校验完成后立即显示图标并更新状态
      setTabValidationResults(validationResults);
      setShowValidationIcons(true);

      // 4. 检查前端校验是否通过
      const frontendValid = Object.values(validationResults).every(
        (result) => result.frontend
      );
      if (!frontendValid) {
        message.error("表单验证失败，请检查必填项");
        return;
      }

      // 5. 前端校验通过后调用提交审核接口（这里会触发后端校验）
      await dataApi.submitSetupApplication({ id: data.enterprise.id });
      message.success("提交审核成功");

      // 6. 刷新数据
      loadData();
    } catch (error: any) {
      // 处理后端校验错误
      if (error?.data && Array.isArray(error.data)) {
        handleValidationErrors(error.data);
        message.error("请完善内容，检查标红的字段");
      } else {
        message.error("提交失败，请重试");
      }
    } finally {
      setSubmitLoading(false);
    }
  };

  // Tab导航函数
  const handlePrevTab = useCallback(() => {
    if (currentIndex > 0) {
      setActiveKey(tabKeys[currentIndex - 1]);
    }
  }, [currentIndex, tabKeys]);

  const handleNextTab = useCallback(() => {
    if (currentIndex < tabKeys.length - 1) {
      setActiveKey(tabKeys[currentIndex + 1]);
    }
  }, [currentIndex, tabKeys]);

  // 表单组件映射
  const FormComponents = {
    enterprise: EnterpriseForm,
    legalRepresentative: LegalRepresentativeForm,
    shareholders: ShareholdersForm,
    managers: ManagersForm,
    materials: MaterialsForm,
  };

  // 动态生成Tab配置
  const items: TabsProps["items"] = useMemo(
    () =>
      TAB_CONFIG.map(({ key, label, formKey }) => {
        const FormComponent = FormComponents[formKey];

        return {
          key,
          label: (
            <span>
              {label}
              {getTabValidationIcon(formKey)}
            </span>
          ),
          forceRender: true,
          children: (
            <div
              style={{
                height: tabContentHeight,
                overflow: "auto",
                display: activeKey === key ? "block" : "none",
              }}
            >
              <FormComponent
                ref={formRefs[formKey]}
                initialValues={data?.[formKey]}
                disabled={isApprovalProgressing}
              />
            </div>
          ),
        };
      }),
    [
      getTabValidationIcon,
      tabContentHeight,
      activeKey,
      data,
      isApprovalProgressing,
      formRefs,
    ]
  );

  return (
    <div
      className="setup-detail"
      style={{ minWidth: "1280px", overflowX: "auto" }}
    >
      {/* 审批进度组件 - 只在特定状态下显示 */}
      {isApprovalProgressing && (
        <Card loading={loading} size="small" style={{ marginBottom: 16 }}>
          <ApprovalProgress approvals={data?.approvals} />
        </Card>
      )}
      <Card size="small">
        <Spin spinning={loading}>
          <Tabs
            activeKey={activeKey}
            onChange={setActiveKey}
            items={items}
            tabBarExtraContent={
              isApprovalProgressing ? null : (
                <Space>
                  <Button
                    icon={<LeftOutlined />}
                    onClick={handlePrevTab}
                    disabled={loading || currentIndex === 0}
                  >
                    上一页
                  </Button>
                  <Button
                    icon={<RightOutlined />}
                    onClick={handleNextTab}
                    disabled={loading || currentIndex === tabKeys.length - 1}
                  >
                    下一页
                  </Button>
                  <Button
                    onClick={() => handleSaveDraft()}
                    style={{ marginLeft: 16 }}
                    loading={saveDraftLoading}
                    disabled={loading || submitLoading}
                  >
                    保存草稿
                  </Button>
                  <Button
                    type="primary"
                    onClick={handleSubmit}
                    loading={submitLoading}
                    disabled={loading || saveDraftLoading}
                  >
                    提交审核
                  </Button>
                </Space>
              )
            }
          />
        </Spin>
      </Card>
    </div>
  );
};

export default SetupApprovalDetail;
