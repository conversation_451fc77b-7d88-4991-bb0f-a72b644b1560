import React, { <PERSON><PERSON><PERSON>, SetStateAction, useState, useEffect } from "react";
import { <PERSON><PERSON>, Modal, Spin } from "antd";
import { dataApi } from "@/apis";
import EnterpriseForm from "@/pages/Enterprise/components/EnterpriseForm";
import LegalRepresentativeForm from "@/pages/Enterprise/components/LegalRepresentativeForm";
import ShareholdersForm from "@/pages/Enterprise/components/ShareholdersForm";
import ManagersForm from "@/pages/Enterprise/components/ManagersForm";
import MaterialsForm from "@/pages/Enterprise/components/MaterialsForm";

interface EnterpriseDetailModalProps {
  id?: any;
  isEnterpriseDetailModalVisible: boolean;
  setIsEnterpriseDetailModalVisible: Dispatch<SetStateAction<boolean>>;
}

const EnterpriseDetailModal: React.FC<EnterpriseDetailModalProps> = (props) => {
  const {
    id,
    isEnterpriseDetailModalVisible,
    setIsEnterpriseDetailModalVisible,
  } = props;
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isEnterpriseDetailModalVisible && id) {
      setLoading(true);
      dataApi
        .postGetFullEnterprise(id)
        .then((data) => {
          setData(data);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [isEnterpriseDetailModalVisible]);

  const onCancel = () => {
    setIsEnterpriseDetailModalVisible(false);
  };

  return (
    <Modal
      title="公司详情"
      open={isEnterpriseDetailModalVisible}
      width={1200}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          关闭
        </Button>,
      ]}
    >
      <div
        style={{
          maxHeight: "calc(100vh - 264px)",
          overflow: "auto",
          paddingRight: 24,
        }}
      >
        <Spin spinning={loading}>
          <EnterpriseForm initialValues={data?.enterprise} disabled />
          <LegalRepresentativeForm
            initialValues={data?.legalRepresentative}
            disabled
          />
          <ShareholdersForm initialValues={data?.shareholders} disabled />
          <ManagersForm initialValues={data?.managers} disabled />
          <MaterialsForm initialValues={data?.materials} disabled />
        </Spin>
      </div>
    </Modal>
  );
};

export default EnterpriseDetailModal;
