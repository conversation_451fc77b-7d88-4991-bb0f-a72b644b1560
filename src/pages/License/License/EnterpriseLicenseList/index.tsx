import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import {
  Table,
  Space,
  Button,
  Flex,
  type TableColumnsType,
  type TablePaginationConfig,
  Input,
  message,
} from "antd";
import { dataApi } from "@/apis";
import EnterpriseDetailModalProps from "../../components/EnterpriseDetailModal";
import PermButton from "@/components/PermButton";

const EnterpriseLicenseList: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<any[]>([]);
  const [currentRecord, setCurrentRecord] = useState<any>();
  const [isEnterpriseDetailModalVisible, setIsEnterpriseDetailModalVisible] =
    useState(false);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [searchText, setSearchText] = useState("");

  const { serviceType } = useParams();

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetEnterpriseLicensePage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        serviceType: serviceType === "all" ? null : serviceType,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handlePrint = (record: any) => {
    // TODO 打印
    message.error("暂未实现该功能");
  };

  const handleView = (record: any) => {
    setCurrentRecord(record);
    setIsEnterpriseDetailModalVisible(true);
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "注册单位",
      dataIndex: "name",
      render: (value) => value ?? "-",
    },
    {
      title: "统一社会信用代码",
      dataIndex: "creditCode",
      minWidth: 132,
      render: (value) => value ?? "-",
    },
    {
      title: "证书编号",
      dataIndex: "licenseNo",
      render: (value) => value || "-",
    },
    {
      title: "联系人",
      dataIndex: "contactName",
      render: (value) => value ?? "-",
    },
    {
      title: "联系电话",
      dataIndex: "contactPhone",
      minWidth: 90,
      render: (value) => value ?? "-",
    },
    {
      title: "保安服务类型",
      dataIndex: "serviceType",
      minWidth: 120,
      render: (value) =>
        ({
          0: "保安培训单位",
          1: "保安服务公司（保安服务分公司）",
          2: "武装守护押运",
          3: "公司自行招用保安员的单位",
          4: "物业",
          5: "跨区域保安服务公司",
        })[value],
    },
    {
      title: "发证日期",
      dataIndex: "licenseDate",
      minWidth: 90,
      render: (value) => value ?? "-",
    },
    {
      title: "是否打印证书",
      dataIndex: "licensePrintFlag",
      minWidth: 132,
      render: (value) => ({ 0: "否", 1: "是" })[value],
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Space size="small">
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            onClick={() => handlePrint(record)}
          >
            打印证书
          </PermButton>
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            onClick={() => handleView(record)}
          >
            公司详情
          </PermButton>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Flex vertical gap={12}>
        <Space>
          <Input
            placeholder="请输入企业名称或统一社会信用代码"
            style={{ width: 280 }}
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <EnterpriseDetailModalProps
        id={currentRecord?.id}
        isEnterpriseDetailModalVisible={isEnterpriseDetailModalVisible}
        setIsEnterpriseDetailModalVisible={setIsEnterpriseDetailModalVisible}
      />
    </div>
  );
};

export default EnterpriseLicenseList;
