import React, { useEffect, useState } from "react";
import {
  Table,
  Space,
  Popconfirm,
  message,
  But<PERSON>,
  Divider,
  Flex,
  Input,
  type TableColumnsType,
  type TablePaginationConfig,
} from "antd";
import { dataApi } from "@/apis";
import EditModal from "./components/EditModal";
import ResetModal from "./components/ResetModal";
import { ISysUser } from "@/apis/data.model";
import "./index.less";
import dayjs from "dayjs";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";

const Staff: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<ISysUser[]>([]);
  const [currentRecord, setCurrentRecord] = useState<ISysUser>();
  const [total, setTotal] = useState(0);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isResetModalVisible, setIsResetModalVisible] = useState(false);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [searchText, setSearchText] = useState("");
  const { user } = useAuth();
  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetSysUserPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleAdd = () => {
    setCurrentRecord(undefined);
    setIsEditModalVisible(true);
  };

  const handleEdit = (record: ISysUser) => {
    setCurrentRecord(record);
    setIsEditModalVisible(true);
  };

  const handleDelete = (record: ISysUser) => {
    dataApi.postDelSysUser(record.id).then(() => {
      message.success("操作成功");
      getDataSourceRequest();
    });
  };

  const handleReset = (record: ISysUser) => {
    setCurrentRecord(record);
    setIsResetModalVisible(true);
  };

  const columns: TableColumnsType<ISysUser> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "姓名",
      dataIndex: "name",
      render: (value) => value || "-",
    },
    {
      title: "所属角色",
      dataIndex: "role",
      minWidth: 90,
      render: (value) =>
        ({ 1: "一级管理员", 2: "二级管理员", 3: "三级管理员" })[value],
    },
    {
      title: "联系电话",
      dataIndex: "phone",
      render: (value) => value || "-",
    },
    {
      title: "单位",
      dataIndex: "organizationName",
      minWidth: 60,
      render: (value) => value || "-",
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "操作",
      key: "action",
      hidden: user?.type === 1,
      render: (_, record) => (
        <>
          <PermButton
            types={[0]}
            roles={[1]}
            type="link"
            size="small"
            key="edit"
            onClick={() => handleEdit(record)}
          >
            修改
          </PermButton>
          <Popconfirm title="确定要删除" onConfirm={() => handleDelete(record)}>
            <PermButton
              types={[0]}
              roles={[1]}
              type="link"
              size="small"
              key="del"
            >
              删除
            </PermButton>
          </Popconfirm>
          <PermButton
            types={[0]}
            roles={[1, 2]}
            type="link"
            size="small"
            key="reset"
            onClick={() => handleReset(record)}
          >
            重置密码
          </PermButton>
        </>
      ),
    },
  ];

  return (
    <div className="staff">
      <Flex vertical gap={12}>
        <Space>
          <Input
            placeholder="请输入姓名或联系方式"
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <PermButton
            types={[0]}
            roles={[1]}
            type="primary"
            onClick={handleAdd}
          >
            添加
          </PermButton>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <EditModal
        id={currentRecord?.id}
        isEditModalVisible={isEditModalVisible}
        setIsEditModalVisible={setIsEditModalVisible}
        tableReload={getDataSourceRequest}
      ></EditModal>
      <ResetModal
        id={currentRecord?.id}
        isResetModalVisible={isResetModalVisible}
        setIsResetModalVisible={setIsResetModalVisible}
        tableReload={getDataSourceRequest}
      ></ResetModal>
    </div>
  );
};

export default Staff;
