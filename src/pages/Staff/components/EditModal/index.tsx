import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Button, Form, Input, Modal, Select, message } from "antd";
import { dataApi } from "@/apis";

interface EditModal {
  id?: string;
  isEditModalVisible: boolean;
  setIsEditModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const EditModal: React.FC<EditModal> = (props) => {
  const { id, isEditModalVisible, setIsEditModalVisible, tableReload } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (isEditModalVisible && id) {
      dataApi.postGetSysUserId(id).then((data) => {
        form.setFieldsValue(data);
      });
    }
  }, [isEditModalVisible]);

  const onFinish = (values: any) => {
    setLoading(true);
    if (id) {
      dataApi
        .postEditSysUser(values)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .finally(() => setLoading(false));
    } else {
      dataApi
        .postSaveSysUser(values)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .finally(() => setLoading(false));
    }
  };

  const onCancel = () => {
    setIsEditModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title={id ? "编辑管理人员" : "添加管理人员"}
      open={isEditModalVisible}
      width={400}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <div style={{ marginRight: 24 }}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 100 } }}
        >
          <Form.Item name="id" hidden>
            <Input></Input>
          </Form.Item>
          <Form.Item name="name" label="姓名" rules={[{ required: true }]}>
            <Input placeholder="请输入姓名" autoComplete="off" />
          </Form.Item>
          <Form.Item name="phone" label="联系方式" rules={[{ required: true }]}>
            <Input placeholder="请输入联系方式" autoComplete="off" />
          </Form.Item>
          {!id && (
            <Form.Item
              name="password"
              label="密码"
              rules={[{ required: true }]}
            >
              <Input.Password placeholder="请输入密码" autoComplete="off" />
            </Form.Item>
          )}
          <Form.Item name="organizationName" label="单位">
            <Input placeholder="请输入单位" autoComplete="off" />
          </Form.Item>
          <Form.Item
            name="role"
            label="角色权限"
            rules={[{ required: true, message: "请选择角色权限" }]}
          >
            <Select
              placeholder="请选择角色权限"
              allowClear
              options={[
                { value: 1, label: "一级管理员" },
                { value: 2, label: "二级管理员" },
                { value: 3, label: "三级管理员" },
              ]}
            />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default EditModal;
