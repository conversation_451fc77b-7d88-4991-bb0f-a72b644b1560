import React, { Dispatch, SetStateAction, useState } from "react";
import { Button, Form, Input, Modal, message } from "antd";
import { dataApi } from "@/apis";

interface ResetModal {
  id: string;
  isResetModalVisible: boolean;
  setIsResetModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const ResetModal: React.FC<ResetModal> = (props) => {
  const { id, isResetModalVisible, setIsResetModalVisible, tableReload } =
    props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    setLoading(true);
    if (id) {
      dataApi
        .postUpdateSysUserPwd(id, values.password)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .finally(() => setLoading(false));
    }
  };

  const onCancel = () => {
    setIsResetModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title="重置密码"
      open={isResetModalVisible}
      width={400}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <div style={{ marginRight: 24 }}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 100 } }}
        >
          <Form.Item
            name="password"
            label="新密码"
            rules={[{ required: true }]}
          >
            <Input.Password placeholder="请输入新密码" autoComplete="off" />
          </Form.Item>
          <Form.Item
            label="确认密码"
            name="password2"
            dependencies={["password"]}
            rules={[
              {
                required: true,
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("password") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error("新密码与确认密码不一致"));
                },
              }),
            ]}
          >
            <Input.Password placeholder="请输入确认密码" autoComplete="off" />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default ResetModal;
