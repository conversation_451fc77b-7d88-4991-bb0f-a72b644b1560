import React, { useEffect, useState } from "react";
import { useParams, useSearchParams } from "react-router";
import {
  Table,
  Space,
  Popconfirm,
  message,
  Button,
  Flex,
  Modal,
  Input,
  type TableColumnsType,
  type TablePaginationConfig,
} from "antd";
import { dataApi } from "@/apis";
import "./index.less";
import AddModal from "./components/AddModal";
import RegistrationEditModal from "../Registration/components/EditModal";
import BatchAddModal from "./components/BatchAddModal";
import { dataToExcel } from "@/utils";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";

const ExaminationDetail: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [currentRecord, setCurrentRecord] = useState<any>();
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [total, setTotal] = useState(0);
  const [isRegistrationEditModalVisible, setIsRegistrationEditModalVisible] =
    useState(false);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isBatchAddModalVisible, setIsBatchAddModalVisible] = useState(false);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [searchText, setSearchText] = useState("");
  const { examUserScheduleId } = useParams();
  const [searchParams] = useSearchParams();
  const level = Number(searchParams.get("level"));
  const { user } = useAuth();
  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetExamUserSchedulePage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        examUserScheduleId,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        setDataSource(data.items);
        setTotal(Number(data.total));
      })
      .finally(() => setLoading(false));
  };

  useEffect(getDataSourceRequest, [pagination, examUserScheduleId]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleDelete = (record: any) => {
    dataApi
      .postDelExamUser2ExamUserSchedule(examUserScheduleId, [record.examUserId])
      .then(() => {
        message.success("操作成功");
        getDataSourceRequest();
      });
  };

  const handleView = (record: any) => {
    setCurrentRecord(record);
    setIsRegistrationEditModalVisible(true);
  };

  const handleBatchDelete = () => {
    Modal.confirm({
      title: "提醒",
      content: `确确定要删除${selectedKeys.length}条记录吗？`,
      onOk: () =>
        dataApi
          .postDelExamUser2ExamUserSchedule(examUserScheduleId, selectedKeys)
          .then(() => {
            message.success("操作成功");
            setSelectedKeys([]);
            getDataSourceRequest();
          }),
    });
  };

  const handleExport = () => {
    setExportLoading(true);
    dataApi
      .postGetExportExamUserScheduleList({
        examUserScheduleId,
        search: searchText,
      })
      .then((data) => {
        const header = [
          { value: "index", label: "序号" },
          { value: "name", label: "姓名" },
          { value: "phone", label: "联系电话" },
          { value: "idCard", label: "身份证号码" },
          { value: "levelDesc", label: "考试名称" },
          { value: "examTime", label: "考试时间" },
          { value: "examType", label: "考试类型" },
          { value: "examAddress", label: "考试地点" },
          { value: "admissionNo", label: "准考证号" },
        ];
        dataToExcel(data, {
          header,
          filename: `考试详情列表-${String(new Date().getTime())}`,
        });
      })
      .finally(() => setExportLoading(false));
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "姓名",
      dataIndex: "name",
      render: (value) => value || "-",
    },
    {
      title: "联系电话",
      dataIndex: "phone",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "身份证号码",
      dataIndex: "idCard",
      render: (value) => value || "-",
    },
    {
      title: "考试名称",
      dataIndex: "levelDesc",
      render: (value) => value || "-",
    },
    {
      title: "考试时间",
      dataIndex: "timeRange",
      render: (_, record) =>
        `${record.examDate} ${record.startTime}-${record.endTime}`,
    },
    {
      title: "考试类型",
      dataIndex: "examAttemptCount",
      render: (value) => `第 ${value} 次考试`,
    },
    {
      title: "考试地点",
      dataIndex: "examAddressName",
      render: (value) => value || "-",
    },
    {
      title: "准考证号",
      dataIndex: "admissionNo",
      render: (value) => value || "-",
    },
    {
      title: "考试试卷",
      dataIndex: "examName",
      minWidth: 90,
      render: (value) => value ?? "-",
    },
    {
      title: "操作",
      key: "action",
      hidden: user?.role === 2,
      render: (_, record) => (
        <>
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            key="view"
            onClick={() => handleView(record)}
          >
            详情
          </PermButton>
          <Popconfirm title="确定要删除" onConfirm={() => handleDelete(record)}>
            <PermButton
              types={[0]}
              roles={[1]}
              type="link"
              size="small"
              key="del"
            >
              删除
            </PermButton>
          </Popconfirm>
        </>
      ),
    },
  ];

  return (
    <div className="examinationDetail">
      <Flex vertical gap={12}>
        <Space>
          <Input
            placeholder="请输入姓名或身份证号码"
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <PermButton
            types={[0]}
            roles={[1]}
            type="primary"
            onClick={() => setIsAddModalVisible(true)}
          >
            添加
          </PermButton>
          <PermButton
            types={[0]}
            roles={[1]}
            type="primary"
            onClick={handleExport}
            loading={exportLoading}
          >
            导出
          </PermButton>
          <PermButton
            types={[0]}
            roles={[1]}
            type="primary"
            onClick={handleBatchDelete}
            disabled={selectedKeys.length === 0}
          >
            批量删除
          </PermButton>
          <PermButton
            types={[0]}
            roles={[1]}
            type="primary"
            onClick={() => {
              setIsBatchAddModalVisible(true);
            }}
          >
            批量添加
          </PermButton>
        </Space>
        <Table
          rowKey="examUserId"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
          rowSelection={{
            selectedRowKeys: selectedKeys,
            onChange: (selectedRowKeys) =>
              setSelectedKeys(selectedRowKeys as Array<string>),
          }}
        />
      </Flex>
      <RegistrationEditModal
        examUserId={currentRecord?.examUserId}
        isEditModalVisible={isRegistrationEditModalVisible}
        setIsEditModalVisible={setIsRegistrationEditModalVisible}
        tableReload={getDataSourceRequest}
        isViewMode
      ></RegistrationEditModal>
      <AddModal
        examUserScheduleId={examUserScheduleId}
        level={level}
        isAddModalVisible={isAddModalVisible}
        setIsAddModalVisible={setIsAddModalVisible}
        tableReload={getDataSourceRequest}
      ></AddModal>
      <BatchAddModal
        examUserScheduleId={examUserScheduleId}
        isBatchAddModalVisible={isBatchAddModalVisible}
        setIsBatchAddModalVisible={setIsBatchAddModalVisible}
        tableReload={getDataSourceRequest}
      ></BatchAddModal>
    </div>
  );
};

export default ExaminationDetail;
