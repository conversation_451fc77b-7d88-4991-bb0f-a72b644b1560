import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import {
  Table,
  Space,
  Button,
  Flex,
  Input,
  Divider,
  type TableColumnsType,
  type TablePaginationConfig,
} from "antd";
import { dataApi } from "@/apis";
import "./index.less";
import PrintAdmissionButton from "./PrintAdmissionButton";
import RegistrationEditModal from "../Registration/components/EditModal";
import dayjs from "dayjs";
import PermView from "@/components/PermView";
import PermButton from "@/components/PermButton";

const AdmissionDetail: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [selectedExamUserIds, setSelectedExamUserIds] = useState<string[]>([]);
  const [currentRecord, setCurrentRecord] = useState<any>();
  const [isRegistrationEditModalVisible, setIsRegistrationEditModalVisible] =
    useState(false);
  const [total, setTotal] = useState(0);
  useState(false);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [searchText, setSearchText] = useState("");
  const { examUserScheduleId } = useParams();

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetExamUserAdmissionPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        examUserScheduleId:
          examUserScheduleId !== "0" ? examUserScheduleId : null,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        setDataSource(data.items);
        setTotal(Number(data.total));
      })
      .finally(() => setLoading(false));
  };

  useEffect(getDataSourceRequest, [pagination, examUserScheduleId]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleView = (record: any) => {
    setCurrentRecord(record);
    setIsRegistrationEditModalVisible(true);
  };

  const handleRowSelectionChange = (selectedRowKeys, selectedRows) => {
    setSelectedKeys(selectedRowKeys);
    setSelectedExamUserIds(selectedRows.map((x) => x.examUserId));
  };

  const afterPrint = () => {
    setSelectedKeys([]);
    setSelectedExamUserIds([]);
    getDataSourceRequest();
  };

  const columns: TableColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "报名编号",
      dataIndex: "registrationNo",
      render: (value) => value || "-",
    },
    {
      title: "姓名",
      dataIndex: "name",
      minWidth: 60,
      render: (value) => value || "-",
    },
    {
      title: "准考证号",
      dataIndex: "admissionNo",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "身份证号码",
      dataIndex: "idCard",
      minWidth: 110,
      render: (value) => value || "-",
    },
    {
      title: "考试名称",
      dataIndex: "levelDesc",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "考试时间",
      dataIndex: "timeRange",
      minWidth: 90,
      render: (_, record) =>
        `${record.examDate} ${record.startTime}-${record.endTime}`,
    },
    {
      title: "考试类型",
      dataIndex: "examAttemptCount",
      minWidth: 90,
      render: (value) => `第 ${value} 次考试`,
    },
    {
      title: "考试地点",
      dataIndex: "examAddressName",
      minWidth: 90,
      render: (value) => value || "-",
    },
    {
      title: "是否打印准考证",
      dataIndex: "admissionPrintFlag",
      minWidth: 132,
      render: (value) => ({ 0: "否", 1: "是" })[value],
    },
    {
      title: "准考证打印时间",
      dataIndex: "admissionPrintTime",
      minWidth: 132,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Flex align="center">
          <PermView types={[0]} roles={[1]}>
            <PrintAdmissionButton
              examUserScheduleId={record.examUserScheduleId}
              examUserIds={[record.examUserId]}
              type="link"
              size="small"
              key="print"
              afterPrint={afterPrint}
            >
              打印准考证
            </PrintAdmissionButton>
          </PermView>
          <PermButton
            types={[0, 1]}
            roles={[1]}
            type="link"
            size="small"
            key="view"
            onClick={() => handleView(record)}
          >
            考生详情
          </PermButton>
        </Flex>
      ),
    },
  ];

  return (
    <div className="admissionDetail">
      <Flex vertical gap={12}>
        <Space>
          <Input
            placeholder="请输入姓名或身份证号码"
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          {examUserScheduleId !== "0" && (
            <PermView types={[0]} roles={[1]}>
              <PrintAdmissionButton
                examUserScheduleId={examUserScheduleId}
                examUserIds={selectedExamUserIds}
                type="primary"
                disabled={selectedKeys.length === 0}
                afterPrint={afterPrint}
              >
                批量打印
              </PrintAdmissionButton>
            </PermView>
          )}
        </Space>
        <Table
          rowKey={(record) =>
            record.examUserScheduleId + ":" + record.examUserId
          }
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
          rowSelection={
            examUserScheduleId !== "0" && {
              selectedRowKeys: selectedKeys,
              onChange: handleRowSelectionChange,
            }
          }
        />
      </Flex>
      <RegistrationEditModal
        examUserId={currentRecord?.examUserId}
        isEditModalVisible={isRegistrationEditModalVisible}
        setIsEditModalVisible={setIsRegistrationEditModalVisible}
        tableReload={getDataSourceRequest}
        isViewMode
      ></RegistrationEditModal>
    </div>
  );
};

export default AdmissionDetail;
