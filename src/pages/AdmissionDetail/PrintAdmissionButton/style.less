@media print {
  // html,
  // body {
  //   height: initial !important;
  //   overflow: initial !important;
  //   -webkit-print-color-adjust: exact;
  //   box-sizing: border-box;
  //   margin: 0;
  //   padding: 0;
  // }
  // .print-admission-button {
  //   @page {
  //     size: A4 landscape;
  //     margin: 0;
  //   }
  // }

  .print-admission-wrapper {
    // position: fixed;
    // z-index: -1000;
    // opacity: 0;
    width: 297mm !important;
    overflow-x: hidden;
    // height: 210mm !important;
    box-sizing: border-box;
    // overflow: hidden;
    overflow-y: auto;
    .a4-page {
      width: 297mm !important;
      height: 209mm !important;
      // padding: 3mm 0 0 4mm;
      font-size: 0;
      overflow: hidden;

      // background: lightblue;
      &:not(:last-child) {
        page-break-after: always;
      }
      &:last-child {
        page-break-after: avoid;
      }
      .page-wrapper {
        display: flex;
        flex-wrap: wrap;
        box-sizing: border-box;
        overflow: hidden;
        width: 100%;
        height: 100%;
        .certificate {
          width: 140mm;
          height: 97mm;
          display: flex;
          .certificate-left {
            width: 70mm;
            padding: 9mm 0;
            border: 0.5mm solid #040000;
            margin-right: 1mm;
            box-sizing: border-box;
            overflow: hidden;
            .certificate-level {
              text-align: center;
              font-weight: 400;
              font-size: 4mm;
              color: #040000;
              height: 5mm;
              line-height: 5mm;
            }
            .certificate-text {
              text-align: center;
              font-weight: 400;
              font-size: 4mm;
              color: #040000;
              height: 5mm;
              line-height: 5mm;
              margin-bottom: 9mm;
            }
            .certificate-photo {
              width: 100%;
              text-align: center;
              margin-bottom: 10mm;
              display: flex;
              align-items: center;
              justify-content: center;
              .photo-pannel {
                border: 0.5mm solid #f9f9f9;
                width: 32mm;
                height: 38mm;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
            }
            .certificate-unit {
              font-weight: 300;
              font-size: 3mm;
              color: #040000;
              line-height: 4mm;
              text-align: center;
            }
            .certificate-time {
              font-weight: 300;
              font-size: 3mm;
              color: #040000;
              line-height: 4mm;
              text-align: center;
            }
          }
          .certificate-right {
            width: 70mm;
            box-sizing: border-box;
            overflow: hidden;
            padding: 6mm 4mm;
            border: 0.5mm solid #040000;
            .entry {
              text-align: right;
              margin-bottom: 8mm;
              .entry-label {
                height: 3mm;
                font-weight: 300;
                font-size: 3mm;
                color: #040000;
                line-height: 4mm;
              }
              .entry-value {
                height: 2mm;
                font-weight: 300;
                font-size: 2mm;
                color: #040000;
              }
            }
            .user {
              display: flex;

              .user-label {
                display: flex;
                margin-right: 2mm;
                flex: 0 0 16mm;
                display: block;
                word-wrap: break-word;
                word-break: break-all;
                padding: 4mm 0 1mm 0;
                font-weight: 300;
                font-size: 3mm;
                color: #040000;
                line-height: 4mm;
                box-sizing: border-box;
                overflow: hidden;
              }
              .user-value {
                flex: 1;
                padding: 4mm 0 1mm 0;
                font-weight: 300;
                font-size: 3mm;
                color: #040000;
                line-height: 3.5mm;
                border-bottom: 0.5mm solid #13213b;
              }
              // .user-label-adress{
              //   position: relative;
              //   top: 0.5mm;
              // }
              // .user-value-adress {
              //   position: relative;
              //   overflow: hidden;
              //   text-decoration: underline;
              //   text-underline-offset: 2.4mm;
              //   line-height: 5mm;
              // }
            }
          }
        }
        .certificate:nth-child(1) {
          margin-top: 3mm;
          margin-bottom: 7mm;
        }
        .certificate:nth-child(2) {
          margin-top: 3mm;
          margin-bottom: 7mm;
        }

        .certificate:nth-child(odd) {
          margin-left: 4mm;
          margin-right: 8mm;
        }
      }
    }
  }
}

.print-admission-button {
  .print-admission-wrapper {
    position: fixed;
    z-index: -1000;
    opacity: 0;
    width: 297mm !important;
    overflow-x: hidden;
    // height: 210mm !important;
    box-sizing: border-box;
    // overflow: hidden;
    overflow-y: auto;
    .a4-page {
      width: 297mm !important;
      height: 209mm !important;
      // padding: 3mm 0 0 4mm;
      font-size: 0;
      overflow: hidden;

      // background: lightblue;
      &:not(:last-child) {
        page-break-after: always;
      }
      &:last-child {
        page-break-after: avoid;
      }
      .page-wrapper {
        display: flex;
        flex-wrap: wrap;
        box-sizing: border-box;
        overflow: hidden;
        width: 297mm !important;
        height: 210mm !important;
        .certificate {
          width: 140mm;
          height: 97mm;
          display: flex;
          .certificate-left {
            width: 70mm;
            padding: 9mm 0;
            border: 0.5mm solid #040000;
            margin-right: 1mm;
            box-sizing: border-box;
            overflow: hidden;
            .certificate-level {
              text-align: center;
              font-weight: 400;
              font-size: 4mm;
              color: #040000;
              height: 5mm;
              line-height: 5mm;
            }
            .certificate-text {
              text-align: center;
              font-weight: 400;
              font-size: 4mm;
              color: #040000;
              height: 5mm;
              line-height: 5mm;
              margin-bottom: 9mm;
            }
            .certificate-photo {
              width: 100%;
              text-align: center;
              margin-bottom: 10mm;
              display: flex;
              align-items: center;
              justify-content: center;
              .photo-pannel {
                border: 0.5mm solid #f9f9f9;
                width: 32mm;
                height: 38mm;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
            }
            .certificate-unit {
              font-weight: 300;
              font-size: 3mm;
              color: #040000;
              line-height: 4mm;
              text-align: center;
            }
            .certificate-time {
              font-weight: 300;
              font-size: 3mm;
              color: #040000;
              line-height: 4mm;
              text-align: center;
            }
          }
          .certificate-right {
            width: 70mm;
            box-sizing: border-box;
            overflow: hidden;
            padding: 6mm 4mm;
            border: 0.5mm solid #040000;
            .entry {
              text-align: right;
              margin-bottom: 8mm;
              .entry-label {
                height: 3mm;
                font-weight: 300;
                font-size: 3mm;
                color: #040000;
                line-height: 4mm;
              }
              .entry-value {
                height: 2mm;
                font-weight: 300;
                font-size: 2mm;
                color: #040000;
              }
            }
            .user {
              display: flex;

              .user-label {
                display: flex;
                margin-right: 2mm;
                flex: 0 0 16mm;
                display: block;
                word-wrap: break-word;
                word-break: break-all;
                padding: 4mm 0 1mm 0;
                font-weight: 300;
                font-size: 3mm;
                color: #040000;
                line-height: 4mm;
                box-sizing: border-box;
                overflow: hidden;
              }
              .user-value {
                flex: 1;
                padding: 4mm 0 1mm 0;
                font-weight: 300;
                font-size: 3mm;
                color: #040000;
                line-height: 3.5mm;
                border-bottom: 0.5mm solid #13213b;
              }
              // .user-label-adress{
              //   position: relative;
              //   top: 0.5mm;
              // }
              // .user-value-adress {
              //   position: relative;
              //   overflow: hidden;
              //   text-decoration: underline;
              //   text-underline-offset: 2.4mm;
              //   line-height: 5mm;
              // }
            }
          }
        }
        .certificate:nth-child(1) {
          margin-top: 3mm;
          margin-bottom: 7mm;
        }
        .certificate:nth-child(2) {
          margin-top: 3mm;
          margin-bottom: 7mm;
        }

        .certificate:nth-child(odd) {
          margin-left: 4mm;
          margin-right: 8mm;
        }
      }
    }
  }
}
