import React, { useEffect, useRef, useState } from "react";
import { Button, ButtonProps, Flex, Radio, RadioProps } from "antd";
import { useReactToPrint } from "react-to-print";
import "./style.less";
import { dataApi } from "@/apis";

interface IPrintButtonProps {
  examUserIds?: string[];
  examUserScheduleId?: string;
  afterPrint?: () => void;
}

const PrintButton: React.FC<IPrintButtonProps & ButtonProps> = (props) => {
  const {
    children,
    examUserScheduleId,
    examUserIds,
    afterPrint,
    ...restProps
  } = props;
  const contentRef = useRef<HTMLDivElement>(null);
  const reactToPrintFn = useReactToPrint({
    contentRef,
    onAfterPrint: afterPrint,
    pageStyle: `
      @page {
        size: A4 landscape !important;
        margin: 0;
      }
      @media print {
        html, body {
          height: initial !important;
          overflow: initial !important;
          -webkit-print-color-adjust: exact;
        }
      }
    `,
  });
  const [groupedAdmissions, setGroupedAdmissions] = useState([]);

  const handleButtonClick = () => {
    if (!examUserScheduleId) return;
    if (!examUserIds?.length) return;
    dataApi
      .postGetPrintExamUserAdmissionList(examUserScheduleId, examUserIds)
      .then((data) => {
        console.log("handlePrint", data);
        const groupedAdmissions = [];
        for (let i = 0; i < data.length; i += 4) {
          groupedAdmissions.push(data.slice(i, i + 4));
        }
        setGroupedAdmissions(groupedAdmissions);
        setTimeout(() => {
          reactToPrintFn();
        }, 100);
      });
  };

  return (
    <div className="print-admission-button">
      <Button {...restProps} onClick={() => handleButtonClick()}>
        {children}
      </Button>
      <div className="print-admission-wrapper" ref={contentRef}>
        {groupedAdmissions?.map((admissions, index) => {
          return (
            <div className="a4-page" key={index}>
              <div className="page-wrapper">
                {admissions.map((admission, idx) => {
                  return (
                    <div className="certificate" key={admission.idCard}>
                      <div className="certificate-left">
                        <div className="certificate-level">
                          {admission.levelDesc}
                        </div>
                        <div className="certificate-text">准考证</div>
                        <div className="certificate-photo">
                          <div className="photo-pannel">
                            <img src={admission?.photoUrl} alt="头像" />
                          </div>
                        </div>

                        <div className="certificate-unit">
                          {admission.organizationalUnitName}
                        </div>
                        <div className="certificate-time">
                          {admission.admissionPrintTime}
                        </div>
                      </div>
                      <div className="certificate-right">
                        <div className="entry">
                          <span className="entry-label">报名编号：</span>
                          <span className="entry-value">
                            {admission.registrationNo}
                          </span>
                        </div>
                        <div className="user">
                          <span className="user-label">准考证号：</span>
                          <span className="user-value">
                            {admission.admissionNo}
                          </span>
                        </div>
                        <div className="user">
                          <span className="user-label">姓名：</span>
                          <span className="user-value">{admission.name}</span>
                        </div>
                        <div className="user">
                          <span className="user-label">公民身份号码：</span>
                          <span className="user-value">{admission.idCard}</span>
                        </div>
                        <div className="user">
                          <span className="user-label">考试日期：</span>
                          <span className="user-value">
                            {admission.examDate}
                          </span>
                        </div>
                        <div className="user">
                          <span className="user-label">考试时间：</span>
                          <span className="user-value">
                            {admission.startTime}-{admission.endTime}
                          </span>
                        </div>
                        <div className="user">
                          <span className="user-label">考试类型：</span>
                          <span className="user-value">
                            第{admission.examAttemptCount}次考试
                          </span>
                        </div>
                        <div className="user">
                          <span className="user-label user-label-adress">
                            考试地址：
                          </span>
                          <span className="user-value user-value-adress">
                            {admission.examAddressName}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default PrintButton;
