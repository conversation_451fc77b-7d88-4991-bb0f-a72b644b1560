import React, { useEffect, useState } from "react";
import {
  Table,
  Space,
  Popconfirm,
  message,
  But<PERSON>,
  Di<PERSON>r,
  Flex,
  type TableColumnsType,
  type TablePaginationConfig,
  DatePicker,
  Input,
  Select,
  Typography,
} from "antd";
import { dataApi } from "@/apis";
import EditPublishModal from "./components/EditPublishModal";
import { INoticePublish } from "@/apis/data.model";
import "./index.less";
import dayjs from "dayjs";
import RecvListModal from "./components/RecvListModal";
import ReadListModal from "./components/ReadListModal";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";

const { Text, Link } = Typography;

const Publish: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<INoticePublish[]>([]);
  const [currentType, setCurrentType] = useState<number>();
  const [currentRecord, setCurrentRecord] = useState<INoticePublish>();
  const [total, setTotal] = useState(0);
  const [isRecvListModalVisible, setIsRecvListModalVisible] = useState(false);
  const [isReadListModalVisible, setIsReadListModalVisible] = useState(false);

  const [isEditPublishVisible, setIsEditPublishVisible] = useState(false);
  const [editType, setEditType] = useState<string>();
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [publishedTime, setPublishedTime] = useState<any>();
  const [noticeType, setNoticeType] = useState(undefined);
  const [searchText, setSearchText] = useState("");
  const [bizStatus, setBizStatus] = useState(undefined);

  const { user } = useAuth();
  const getDataSourceRequest = () => {
    setLoading(true);
    dataApi
      .getNoticePublishPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        publishedTime: publishedTime,
        noticeType: noticeType,
        search: searchText,
        bizStatus: bizStatus,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleAdd = () => {
    setEditType("add");
    setCurrentRecord(undefined);
    setIsEditPublishVisible(true);
  };

  const handlePublish = (record: INoticePublish) => {
    dataApi.saveNoticePublish(record.id).then(() => {
      message.success("操作成功");
      getDataSourceRequest();
    });
  };

  const handleWithdraw = (record: INoticePublish) => {
    dataApi.withdrawNoticePublish(record.id).then(() => {
      message.success("操作成功");
      getDataSourceRequest();
    });
  };

  const handleDelete = (record: INoticePublish) => {
    dataApi.delNoticePublish(record.id).then(() => {
      message.success("操作成功");
      getDataSourceRequest();
    });
  };

  const handleEdit = (record: INoticePublish) => {
    if (record.bizStatus === 1) {
      setEditType("view");
    } else {
      setEditType("edit");
    }

    setCurrentRecord(record);
    setIsEditPublishVisible(true);
  };

  const columns: TableColumnsType<INoticePublish> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "通知类型",
      dataIndex: "noticeType",
      width: 180,
      render: (value) => {
        switch (value) {
          case 0:
            return "日常通知通告";
          case 1:
            return "协查通报";
          default:
            return "-";
        }
      },
    },
    {
      title: "通知标题",
      dataIndex: "title",
      width: 400,
      render: (value) => {
        return (
          <Typography.Paragraph
            style={{
              margin: 0,
              padding: 0,

              cursor: "pointer",
            }}
            ellipsis={{
              rows: 3,
              symbol: "...",
              tooltip: { title: value, arrow: false },
            }}
          >
            {value}
          </Typography.Paragraph>
        );
      },
    },
    {
      title: "发布时间",
      dataIndex: "publishedTime",
      minWidth: 100,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "发布部门",
      dataIndex: "publishDepartment",
      minWidth: 100,
      render: (value) => value || "-",
    },
    {
      title: "接收公司",
      dataIndex: "receiveContent",
      minWidth: 120,
      render: (value, record) => {
        return (
          <Link
            color="primary"
            onClick={() => {
              setCurrentType(undefined);
              setCurrentRecord(record);
              setIsReadListModalVisible(true);
            }}
          >
            {value}
          </Link>
        );
      },
    },
    {
      title: "状态",
      dataIndex: "bizStatus",
      width: 80,
      render: (value) => {
        switch (value) {
          case 0:
            return "草稿";
          case 1:
            return "发布";
          default:
            return "-";
        }
      },
    },
    {
      title: "已读数",
      dataIndex: "readCount",
      width: 80,
      render: (value, record) => {
        return (
          <Link
            color="primary"
            onClick={() => {
              setCurrentType(1);
              setCurrentRecord(record);
              setIsReadListModalVisible(true);
            }}
          >
            {value}
          </Link>
        );
      },
    },
    {
      title: "未读数",
      dataIndex: "unreadCount",
      width: 80,
      render: (value, record) => {
        return (
          <Link
            color="primary"
            onClick={() => {
              setCurrentType(0);
              setCurrentRecord(record);
              setIsReadListModalVisible(true);
            }}
          >
            {value}
          </Link>
        );
      },
    },
    {
      title: "操作",
      key: "action",
      width: 240,
      fixed: "right",
      hidden: user?.type === 1 || (user.type === 0 && user.role === 2),
      render: (_, record) => (
        <>
          {record.bizStatus === 0 && (
            <PermButton
              types={[0]}
              roles={[1]}
              type="link"
              size="small"
              key="publish"
              onClick={() => handlePublish(record)}
            >
              发布
            </PermButton>
          )}

          {record.bizStatus === 1 && (
            <PermButton
              types={[0]}
              roles={[1]}
              type="link"
              size="small"
              key="withdraw"
              onClick={() => handleWithdraw(record)}
            >
              撤回
            </PermButton>
          )}
          <PermButton
            types={[0]}
            roles={[1]}
            type="link"
            size="small"
            key="edit"
            onClick={() => handleEdit(record)}
          >
            查看编辑
          </PermButton>
          <Popconfirm title="确定要删除" onConfirm={() => handleDelete(record)}>
            <PermButton
              types={[0]}
              roles={[1]}
              type="link"
              size="small"
              key="del"
            >
              删除
            </PermButton>
          </Popconfirm>
        </>
      ),
    },
  ];

  const handleClear = () => {
    setNoticeType(undefined);
    setBizStatus(undefined);
    setSearchText("");
    handleSearch();
  };

  return (
    <div className="publish-page">
      <Flex vertical gap={12}>
        <Space>
          <Select
            placeholder="请选择通知类型"
            allowClear
            style={{ width: 200 }}
            value={noticeType}
            onChange={(val) => {
              setNoticeType(val);
              handleSearch();
            }}
            onClear={handleClear}
            options={[
              { value: "", label: "所有类型" },
              { value: 0, label: "日常通知通告" },
              { value: 1, label: "协查通报" },
            ]}
          />
          <Select
            placeholder="请选择状态"
            allowClear
            style={{ width: 200 }}
            value={bizStatus}
            onChange={(val) => {
              setBizStatus(val);
              handleSearch();
            }}
            onClear={handleClear}
            options={[
              { value: "", label: "所有状态" },
              { value: 0, label: "草稿" },
              { value: 1, label: "发布" },
            ]}
          />
          <Input
            placeholder="请输入通知标题"
            allowClear
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleClear}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <PermButton
            types={[0]}
            roles={[1]}
            type="primary"
            onClick={handleAdd}
          >
            发布通知
          </PermButton>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <RecvListModal
        id={currentRecord?.id}
        isRecvListModalVisible={isRecvListModalVisible}
        setIsRecvListModalVisible={setIsRecvListModalVisible}
        tableReload={getDataSourceRequest}
      ></RecvListModal>
      <ReadListModal
        type={currentType}
        id={currentRecord?.id}
        isReadListModalVisible={isReadListModalVisible}
        setIsReadListModalVisible={setIsReadListModalVisible}
        tableReload={getDataSourceRequest}
      ></ReadListModal>

      <EditPublishModal
        id={currentRecord?.id}
        type={editType}
        isEditPublishVisible={isEditPublishVisible}
        setIsEditPublishVisible={setIsEditPublishVisible}
        tableReload={getDataSourceRequest}
      ></EditPublishModal>
    </div>
  );
};

export default Publish;
