.ant-upload-list-text {
  padding: 10px 0;
}

.edit-publish-modal {
  .react-quill {
    .ql-toolbar {
      border-style: solid;
      border: 1px solid #d9d9d9;
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
    }
    .ql-container {
      border-style: solid;
      border: 1px solid #d9d9d9;
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
      .ql-editor {
        padding: 8px 12px;
        height: 100px;
      }
      .ql-editor.ql-blank::before {
        font-style: normal;
        color: rgba(0, 0, 0, 0.25);
        font-weight: 500;
        left: 12px;
      }
    }
  }
}
