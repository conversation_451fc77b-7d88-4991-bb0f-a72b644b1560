import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Button,
  Card,
  Flex,
  Form,
  Input,
  List,
  Modal,
  Select,
  Space,
  TreeSelect,
  Typography,
  Upload,
  UploadFile,
  UploadProps,
  message,
} from "antd";
import {
  DeleteOutlined,
  DownloadOutlined,
  EyeOutlined,
  LinkOutlined,
} from "@ant-design/icons";
import { dataApi } from "@/apis";
import { uploadFileUrl } from "@/config";
import ReactQuill, { UnprivilegedEditor } from "react-quill";
import "react-quill/dist/quill.snow.css";
const { Text, Link } = Typography;
import "./index.less";
import {
  countLevelTwoNodes,
  downloadFile,
  findObjectsById,
  getFileTypeFromURL,
} from "@/utils";
import { useAuth } from "@/Auth";
interface EditPublish {
  id?: string;
  type: string;
  isEditPublishVisible: boolean;
  setIsEditPublishVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const EditPublish: React.FC<EditPublish> = (props) => {
  const {
    id,
    type,
    isEditPublishVisible,
    setIsEditPublishVisible,
    tableReload,
  } = props;
  const [loading, setLoading] = useState(false);
  const [treeData, setTreeData] = useState([]);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<any[]>([]);
  const { user } = useAuth();
  useEffect(() => {
    if (isEditPublishVisible && id) {
      if (user.type == 1) {
        dataApi.getNoticeView(id).then((data) => {
          setFileList(data.files);
          form.setFieldsValue({
            ...data,
            publishDepartment:
              type === "view" && !data.publishDepartment
                ? " "
                : data.publishDepartment,
            content: type === "view" && !data.content ? " " : data.content,
            enterpriseIds: data.receiveContent, // 强塞
          });
        });
      } else {
        dataApi.getNoticePublish(id).then((data) => {
          setFileList(data.files);
          form.setFieldsValue({
            ...data,
            publishDepartment:
              type === "view" && !data.publishDepartment
                ? " "
                : data.publishDepartment,
            content: type === "view" && !data.content ? " " : data.content,
          });
        });
      }
    }
    if (isEditPublishVisible && user.type === 0) {
      dataApi.getSearchNoticePublishEnterpriseList().then((data) => {
        setTreeData(data);
      });
    }
  }, [isEditPublishVisible]);

  const onFinish = (values: any) => {

    if (type === "view") {
      setIsEditPublishVisible(false);
      return;
    }

    let receiveContent = "";

    const selectedIds = values.enterpriseIds || [];
    const matchedObjects = findObjectsById(treeData, selectedIds);
    const levelTwoNodeCount = countLevelTwoNodes(treeData);

    if (selectedIds.length === levelTwoNodeCount) {
      receiveContent = "全体公司";
    } else if (selectedIds?.length <= 2) {
      receiveContent = matchedObjects?.map((item) => item.name).join(",");
    } else {
      receiveContent = `${matchedObjects?.[0].name},${matchedObjects?.[1].name}等${matchedObjects.length}个公司`;
    }

    const params: any = {
      id: values.id,
      noticeType: values.noticeType,
      title: values.title,
      publishDepartment: values.publishDepartment,
      content: values.content,
      receiveType: values.enterpriseIds?.lenght === treeData?.length ? 0 : 1,
      receiveContent: receiveContent,
      enterpriseIds: values.enterpriseIds,
      files: fileList?.map((item) => {
        return {
          id: item.id,
          name: item.name,
          path: item.path,
        };
      }),
    };

    setLoading(true);
    dataApi
      .saveNoticeDraft(params)
      .then(() => {
        message.success("操作成功");
        tableReload();
        onCancel();
      })
      .finally(() => setLoading(false));
  };

  const onCancel = () => {
    setIsEditPublishVisible(false);
    form.resetFields();
    setFileList([]);
  };

  const handleFileChange = (info) => {
    let newFileList = [...info.fileList];
    newFileList = newFileList.map((file) => {
      if (file.response) {
        file.path = file.response.data.path;
        file.name = file.response.data.fileName;
        file.url = file.response.data.url;
      }
      return file;
    });
    setFileList(newFileList);
  };

  return (
    <Modal
      title="通知"
      open={isEditPublishVisible}
      width={600}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        type === "view" ? null : (
          <Button key="cancel" onClick={onCancel}>
            取消
          </Button>
        ),
        ,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确认
        </Button>,
      ]}
    >
      <div className="edit-publish-modal" style={{ marginRight: 24 }}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          disabled={type === "view"}
          labelCol={{ style: { width: 100 } }}
        >
          <Form.Item name="id" hidden>
            <Input></Input>
          </Form.Item>
          <Form.Item
            name="noticeType"
            label="通知类型"
            rules={[{ required: true, message: "请选择通知类型" }]}
          >
            <Select
              placeholder="请选择通知类型"
              allowClear
              options={[
                { value: 0, label: "日常通知通告" },
                { value: 1, label: "协查通报" },
              ]}
            />
          </Form.Item>
          <Form.Item name="title" label="通知标题" rules={[{ required: true }]}>
            <Input placeholder="请输入通知标题" autoComplete="off" />
          </Form.Item>
          <Form.Item name="publishDepartment" label="发布部门">
            <Input placeholder="请输入发布部门" autoComplete="off" />
          </Form.Item>
          <Form.Item
            name="enterpriseIds"
            label="接收公司"
            rules={[{ required: true }]}
          >
            <TreeSelect
              showSearch
              fieldNames={{
                label: "name",
                value: "id",
              }}
              treeData={treeData}
              maxTagCount={4}
              filterTreeNode={(inputValue, treeNode) => {
                return treeNode.props.name
                  .toLowerCase()
                  .includes(inputValue.toLowerCase());
              }}
              multiple
              maxCount={100}
              style={{ width: "100%" }}
              treeDefaultExpandAll={true}
              treeCheckable
              placeholder="请选择接收公司"
              showCheckedStrategy={TreeSelect.SHOW_CHILD}
            />
          </Form.Item>
          <Form.Item name="content" label="正文">
            <ReactQuill
              theme="snow"
              className="react-quill"
              placeholder="请输入正文"
              readOnly={type === "view"}
              modules={{ toolbar: true }}
            />
          </Form.Item>

          <Form.Item name="organizationName" label="附件">
            <Upload
              fileList={fileList}
              action={uploadFileUrl}
              multiple={true}
              data={{
                type: 4,
              }}
              accept=".jpg,.png,.pdf"
              showUploadList={true}
              onChange={handleFileChange}
              style={{ padding: "10px 0" }}
              itemRender={(
                originNode,
                file,
                fileList,
                { remove, download }
              ) => {
                return (
                  <Flex justify="space-between" align="center" gap={8}>
                    <Flex flex={1} gap={12} align="center">
                      <LinkOutlined />

                      <Typography.Paragraph
                        style={{
                          margin: 0,
                          padding: 0,
                          cursor: "pointer",
                          width: "310px",
                          overflow: "hidden",
                        }}
                        ellipsis={{
                          rows: 1,
                          symbol: "...",
                          tooltip: file.name,
                        }}
                      >
                        {file.name}
                      </Typography.Paragraph>
                    </Flex>

                    <Flex gap={0}>
                      <Button
                        color="default"
                        variant="text"
                        disabled={false}
                        onClick={() => {
                          const data = fileList.find(
                            (item) => item.uid === file.uid
                          );
                          const fileType = getFileTypeFromURL(data.url);
                          console.log(fileType);
                          // if (fileType === "pdf") {
                          //   window.open(data.url, "_blank");
                          // }
                          // if (fileType === "jpg" || fileType === "png") {
                          //   window.open(data.url, "_blank");
                          // }
                          window.open(data.url, "_blank");
                        }}
                        icon={<EyeOutlined />}
                      ></Button>

                      <Button
                        color="default"
                        variant="text"
                        disabled={false}
                        onClick={() => {
                          const data = fileList.find(
                            (item) => item.uid === file.uid
                          );
                          downloadFile(data.url, data.name);
                        }}
                        icon={<DownloadOutlined />}
                      ></Button>

                      {type !== "view" && (
                        <Button
                          color="default"
                          variant="text"
                          onClick={remove}
                          icon={<DeleteOutlined />}
                        ></Button>
                      )}
                    </Flex>
                  </Flex>
                );
              }}
            >
              <Flex gap={12} align="center">
                <Button type="primary">点击上传</Button>
                <span>仅支持png、jpg、pdf格式</span>
              </Flex>
            </Upload>
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default EditPublish;
