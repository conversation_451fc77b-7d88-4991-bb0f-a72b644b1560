import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Button,
  Flex,
  Form,
  Input,
  List,
  Modal,
  Select,
  Table,
  TableColumnsType,
  TablePaginationConfig,
  message,
} from "antd";
import { dataApi } from "@/apis";
import { ISysUser } from "@/apis/data.model";

interface UnreadListModalModal {
  id?: string;
  isUnreadListModalVisible: boolean;
  setIsUnreadListModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const UnreadListModalModal: React.FC<UnreadListModalModal> = (props) => {
  const {
    id,
    isUnreadListModalVisible,
    setIsUnreadListModalVisible,
    tableReload,
  } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [total, setTotal] = useState(0);
  const [dataSource, seteDataSource] = useState<ISysUser[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
  });

  useEffect(() => {
    if (isUnreadListModalVisible && id) {
      dataApi.postGetSysUserId(id).then((data) => {
        form.setFieldsValue(data);
      });
      setLoading(true);
      dataApi
        .postGetSysUserPage({
          pageSize: pagination.pageSize || 10,
          pageNum: pagination.current || 1,
        })
        .then((data) => {
          setLoading(false);
          seteDataSource(data.items);
          setTotal(Number(data.total));
        })
        .catch(() => {
          setLoading(false);
        });
    }
  }, [isUnreadListModalVisible]);

  const onCancel = () => {
    setIsUnreadListModalVisible(false);
    form.resetFields();
  };

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const columns: TableColumnsType<ISysUser> = [
    {
      title: "单位名称",
      dataIndex: "name",
      render: (value) => value || "-",
    },
    {
      title: "统一社会信用代码",
      dataIndex: "name",
      render: (value) => value || "-",
    },
  ];

  return (
    <Modal
      title="未读数"
      open={isUnreadListModalVisible}
      width={600}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button
          key="confirm"
          type="primary"
          onClick={onCancel}
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <Table
        rowKey="id"
        columns={columns}
        tableLayout="auto"
        scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
        dataSource={dataSource}
        loading={loading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          showSizeChanger: true,
          showTotal: (x) => `共 ${x} 条记录`,
          total,
        }}
        onChange={handleTableChange}
      />
    </Modal>
  );
};

export default UnreadListModalModal;
