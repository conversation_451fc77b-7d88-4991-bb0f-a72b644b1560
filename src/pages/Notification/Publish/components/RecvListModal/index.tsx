import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Button, Flex, Form, Input, List, Modal, Select, message } from "antd";
import { dataApi } from "@/apis";

interface RecvListModal {
  id?: string;
  isRecvListModalVisible: boolean;
  setIsRecvListModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const RecvListModal: React.FC<RecvListModal> = (props) => {
  const { id, isRecvListModalVisible, setIsRecvListModalVisible, tableReload } =
    props;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [form] = Form.useForm();

  useEffect(() => {
    if (isRecvListModalVisible && id) {
      dataApi
        .getNoticePublishEnterpriseList({ id, type: undefined })
        .then((data) => {
          setDataSource(data);
        });
    }
  }, [isRecvListModalVisible]);

  const onCancel = () => {
    setIsRecvListModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title="接受公司列表"
      open={isRecvListModalVisible}
      width={600}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button
          key="confirm"
          type="primary"
          onClick={onCancel}
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <List
        itemLayout="horizontal"
        dataSource={dataSource}
        renderItem={(item, index) => <List.Item>{item.name}</List.Item>}
      />
    </Modal>
  );
};

export default RecvListModal;
