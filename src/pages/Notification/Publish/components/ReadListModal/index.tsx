import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Button,
  Flex,
  Form,
  Modal,
  Table,
  TableColumnsType,
  TablePaginationConfig,
} from "antd";
import { dataApi } from "@/apis";
import { ISysUser } from "@/apis/data.model";

interface ReadListModalModal {
  id: string;
  type: number;
  isReadListModalVisible: boolean;
  setIsReadListModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const ReadListModalModal: React.FC<ReadListModalModal> = (props) => {
  const {
    id,
    type,
    isReadListModalVisible,
    setIsReadListModalVisible,
    tableReload,
  } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [total, setTotal] = useState(0);
  const [dataSource, setDataSource] = useState([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
  });

  useEffect(() => {
    if (isReadListModalVisible && id) {
      dataApi.getNoticePublishEnterpriseList({ id, type }).then((data) => {
        setDataSource(data);
      });
    }
  }, [isReadListModalVisible]);

  const onCancel = () => {
    setIsReadListModalVisible(false);
    form.resetFields();
  };

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const columns: TableColumnsType<ISysUser> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "公司名称",
      dataIndex: "name",
      render: (value) => value || "-",
    },
    // {
    //   title: "统一社会信用代码",
    //   dataIndex: "name",
    //   render: (value) => value || "-",
    // },
  ];

  return (
    <Modal
      title={type === 0 ? "未读数" : type === 1 ? "已读数" : "接收公司"}
      open={isReadListModalVisible}
      width={600}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button
          key="confirm"
          type="primary"
          onClick={onCancel}
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <Flex vertical gap={12} style={{ paddingTop: 12 }}>
        <div style={{ color: "#666" }}>共{dataSource?.length}家</div>
        <Table
          rowKey="id"
          bordered
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={false}
          onChange={handleTableChange}
        />
      </Flex>
    </Modal>
  );
};

export default ReadListModalModal;
