import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Button, Flex, Form, Input, Modal, Select, message } from "antd";
import { dataApi } from "@/apis";

interface ReplyModal {
  id?: string;
  isReplyModalVisible: boolean;
  setIsReplyModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const ReplyModal: React.FC<ReplyModal> = (props) => {
  const { id, isReplyModalVisible, setIsReplyModalVisible, tableReload } =
    props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (isReplyModalVisible && id) {
      dataApi.postGetSysUserId(id).then((data) => {
        form.setFieldsValue(data);
      });
    }
  }, [isReplyModalVisible]);

  const onFinish = (values: any) => {
    setLoading(true);
    if (id) {
      dataApi
        .postEditSysUser(values)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .finally(() => setLoading(false));
    } else {
      dataApi
        .postSaveSysUser(values)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .finally(() => setLoading(false));
    }
  };

  const onCancel = () => {
    setIsReplyModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title={id ? "留言回复" : "留言回复"}
      open={isReplyModalVisible}
      width={600}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <Flex vertical gap={12} style={{ marginRight: 24 }}>
        <Flex vertical className="top">
          <Flex>
            <Flex flex="0 0 60px" align="center">
              企业1
            </Flex>
            <Flex flex="1" align="center">
              留言内容:申请下一场考试
            </Flex>
          </Flex>
          <Flex justify="end">
            <Flex flex="0 0 60px" align="center">
              admin
            </Flex>
            <Flex flex="1" align="center">
              好的
            </Flex>
          </Flex>
        </Flex>
        <Flex className="bottom">
          <Flex flex="0 0 60px" align="center">
            回复:
          </Flex>
          <Flex flex={1}>
            <Input.TextArea
              rows={3}
              placeholder="请输入回复内容"
              autoComplete="off"
            />
          </Flex>
        </Flex>
      </Flex>
    </Modal>
  );
};

export default ReplyModal;
