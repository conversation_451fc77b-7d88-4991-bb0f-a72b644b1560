import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Button,
  Card,
  Flex,
  Form,
  Input,
  List,
  Modal,
  Select,
  Upload,
  UploadFile,
  UploadProps,
  message,
} from "antd";
import { dataApi } from "@/apis";
import { fileUploadUrl } from "@/config";

interface ViewModal {
  id?: string;
  isViewModalVisible: boolean;
  setIsViewModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const ViewModal: React.FC<ViewModal> = (props) => {
  const { id, isViewModalVisible, setIsViewModalVisible, tableReload } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([
    {
      uid: "-1",
      name: "xxx.png",
      status: "done",
      url: "http://www.baidu.com/xxx.png",
    },
  ]);

  useEffect(() => {
    if (isViewModalVisible && id) {
      dataApi.postGetSysUserId(id).then((data) => {
        form.setFieldsValue(data);
      });
    }
  }, [isViewModalVisible]);

  const onFinish = (values: any) => {
    setLoading(true);
    if (id) {
      dataApi
        .postEditSysUser(values)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .finally(() => setLoading(false));
    } else {
      dataApi
        .postSaveSysUser(values)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .finally(() => setLoading(false));
    }
  };

  const onCancel = () => {
    setIsViewModalVisible(false);
    form.resetFields();
  };

  const handleFileChange: UploadProps["onChange"] = (info) => {
    let newFileList = [...info.fileList];
    newFileList = newFileList.slice(-2);
    newFileList = newFileList.map((file) => {
      if (file.response) {
        file.url = file.response.url;
      }
      return file;
    });
    setFileList(newFileList);
  };

  return (
    <Modal
      title={id ? "编辑管理人员" : "添加管理人员"}
      open={isViewModalVisible}
      width={600}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <div style={{ marginRight: 24 }}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 100 } }}
        >
          <Form.Item name="id" hidden>
            <Input></Input>
          </Form.Item>
          <Form.Item
            name="role"
            label="通知类型"
            rules={[{ required: true, message: "请选择通知类型" }]}
          >
            <Select
              placeholder="请选择角色权限"
              allowClear
              options={[
                { value: 1, label: "test1" },
                { value: 2, label: "test2" },
                { value: 3, label: "test3" },
              ]}
            />
          </Form.Item>
          <Form.Item name="name" label="通知标题" rules={[{ required: true }]}>
            <Input placeholder="请输入通知标题" autoComplete="off" />
          </Form.Item>
          <Form.Item name="phone" label="发布部门">
            <Input placeholder="请输入发布部门" autoComplete="off" />
          </Form.Item>
          <Form.Item name="phone" label="正文">
            <Input.TextArea
              rows={3}
              placeholder="请输入正文"
              autoComplete="off"
            />
          </Form.Item>

          <Form.Item name="organizationName" label="附件">
            <Upload
              fileList={fileList}
              action={fileUploadUrl}
              multiple={true}
              accept=".jpg,.jpeg,.png"
              showUploadList={true}
              onChange={handleFileChange}
            >
              <Flex gap={12} align="center">
                <Button type="primary">上传图片</Button>
                <span>仅支持png、jpg、pdf格式</span>
              </Flex>
            </Upload>
          </Form.Item>

          {/* <Form.Item name="organizationName" label="附件">
            <List
              itemLayout="horizontal"
              dataSource={[
                {
                  title: "Ant Design Title 1",
                },
                {
                  title: "Ant Design Title 2",
                },
                {
                  title: "Ant Design Title 3",
                },
                {
                  title: "Ant Design Title 4",
                },
              ]}
              renderItem={(item, index) => {
                return (
                  <Card style={{ marginBottom: "8px" }}>Card content</Card>
                );
              }}
            />
          </Form.Item> */}
        </Form>
      </div>
    </Modal>
  );
};

export default ViewModal;
