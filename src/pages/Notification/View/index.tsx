import React, { useEffect, useState } from "react";
import {
  Table,
  Space,
  Button,
  Flex,
  type TableColumnsType,
  type TablePaginationConfig,
  DatePicker,
  Input,
  Select,
  Typography,
} from "antd";
import { dataApi } from "@/apis";
import ViewModal from "./components/ViewModal";
import { INoticePublish } from "@/apis/data.model";
import "./index.less";
import dayjs from "dayjs";
import EditPublishModal from "../Publish/components/EditPublishModal";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";

const Publish: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<INoticePublish[]>([]);
  const [currentRecord, setCurrentRecord] = useState<INoticePublish>();
  const [total, setTotal] = useState(0);
  const [isLeaveModalVisible, setIsLeaveModalVisible] = useState(false);
  const [isEditPublishVisible, setIsEditPublishVisible] = useState(false);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [publishedTime, setPublishedTime] = useState<any>();
  const [noticeType, setNoticeType] = useState(undefined);
  const [searchText, setSearchText] = useState("");
  const { user } = useAuth();
  const getDataSourceRequest = () => {
    setLoading(true);
    dataApi
      .getNoticeViewPage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        publishedTime: publishedTime,
        noticeType: noticeType,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleView = (record: INoticePublish) => {
    setCurrentRecord(record);
    setIsEditPublishVisible(true);
  };

  const columns: TableColumnsType<INoticePublish> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "通知类型",
      dataIndex: "noticeType",
      width: 180,
      render: (value) => {
        switch (value) {
          case 0:
            return "日常通知通告";
          case 1:
            return "协查通报";
          default:
            return "-";
        }
      },
    },
    {
      title: "通知标题",
      dataIndex: "title",
      width: 400,
      render: (value) => {
        return (
          <Typography.Paragraph
            style={{
              margin: 0,
              padding: 0,

              cursor: "pointer",
            }}
            ellipsis={{
              rows: 3,
              symbol: "...",
              tooltip: { title: value, arrow: false },
            }}
          >
            {value}
          </Typography.Paragraph>
        );
      },
    },
    {
      title: "发布时间",
      dataIndex: "publishedTime",
      width: 200,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "发布部门",
      dataIndex: "publishDepartment",
      width: 200,
      render: (value) => value || "-",
    },
    {
      title: "操作",
      key: "action",
      width: 240,
      hidden: user?.type === 0,
      fixed: "right",
      render: (_, record) => (
        <>
          <PermButton
            types={[1]}
            roles={[0]}
            type="link"
            size="small"
            key="view"
            onClick={() => handleView(record)}
          >
            查看
          </PermButton>
        </>
      ),
    },
  ];

  const handleClear = () => {
    setNoticeType(undefined);
    setSearchText("");
    handleSearch();
  };

  return (
    <div className="view-page">
      <Flex vertical gap={12}>
        <Space>
          <Select
            placeholder="请选择通告类型"
            allowClear
            style={{ width: 200 }}
            value={noticeType}
            onChange={(val) => {
              setNoticeType(val);
              handleSearch();
            }}
            onClear={handleClear}
            options={[
              { value: "", label: "所有类型" },
              { value: 0, label: "日常通知通告" },
              { value: 1, label: "协查通报" },
            ]}
          />
          {/* <DatePicker
            placeholder="发布时间"
            allowClear
            style={{ width: 200 }}
            onChange={(date) => {
              setPublishedTime(date);
              handleSearch();
            }}
          /> */}
          <Input
            placeholder="请输入通知标题"
            allowClear
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleClear}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>

      <EditPublishModal
        id={currentRecord?.id}
        type="view"
        isEditPublishVisible={isEditPublishVisible}
        setIsEditPublishVisible={setIsEditPublishVisible}
        tableReload={getDataSourceRequest}
      ></EditPublishModal>
    </div>
  );
};

export default Publish;
