import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Button, Flex, Form, Input, Modal, Select, message } from "antd";
import { dataApi } from "@/apis";

interface LeaveModal {
  id?: string;
  isLeaveModalVisible: boolean;
  setIsLeaveModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const LeaveModal: React.FC<LeaveModal> = (props) => {
  const { id, isLeaveModalVisible, setIsLeaveModalVisible, tableReload } =
    props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  // useEffect(() => {
  //   if (isLeaveModalVisible && id) {
  //     dataApi.postGetSysUserId(id).then((data) => {
  //       form.setFieldsValue(data);
  //     });
  //   }
  // }, [isLeaveModalVisible]);

  const onFinish = (values: any) => {
    setLoading(true);
    if (id) {
      // dataApi
      //   .postEditSysUser(values)
      //   .then(() => {
      //     message.success("操作成功");
      //     tableReload();
      //     onCancel();
      //   })
      //   .finally(() => setLoading(false));
    } else {
      dataApi
        .postSaveMessage(values)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .finally(() => setLoading(false));
    }
  };

  const onCancel = () => {
    setIsLeaveModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title={id ? "编辑留言" : "添加留言"}
      open={isLeaveModalVisible}
      width={600}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <Form
        preserve={false}
        form={form}
        onFinish={onFinish}
        name="modal"
        labelAlign="right"
        labelCol={{ style: { width: 60 } }}
      >
        <Form.Item name="content" label="留言" rules={[{ required: true }]}>
          <Input.TextArea
            rows={6}
            placeholder="请输入留言"
            autoComplete="off"
            maxLength={1000}
            showCount
            autoSize={{ minRows: 5, maxRows: 5 }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default LeaveModal;
