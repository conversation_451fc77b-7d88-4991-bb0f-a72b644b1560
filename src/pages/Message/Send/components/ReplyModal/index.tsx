import React, {
  Dispatch,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";
import { Button, Flex, Form, Input, Modal, Select, Space, message } from "antd";
import { dataApi } from "@/apis";
import {
  CheckCircleFilled,
  ClockCircleFilled,
  EyeFilled,
  MinusCircleFilled,
  SendOutlined,
} from "@ant-design/icons";
import "./index.less";
import { IMessageItem } from "@/apis/data.model";
import dayjs from "dayjs";
interface ReplyModal {
  id?: string;
  isReplyModalVisible: boolean;
  setIsReplyModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const ReplyModal: React.FC<ReplyModal> = (props) => {
  const { id, isReplyModalVisible, setIsReplyModalVisible, tableReload } =
    props;
  const [loading, setLoading] = useState(false);
  const [inputText, setInputText] = useState("");
  const [content, setContent] = useState("");
  const containRef = useRef<HTMLDivElement>(null);
  const [historys, setHistorys] = useState<IMessageItem[]>();

  useEffect(() => {
    const current = containRef.current;
    if (current) {
      current.scrollTo({ top: current.scrollHeight });
    }
  }, [historys]);

  const getMessageReplyListRequest = () => {
    dataApi.postGetMessageReplyList({ id }).then((data) => {
      setContent(data.content);
      setHistorys(data.items);
    });
  };

  useEffect(() => {
    setInputText("");
    setInputText("");
    setHistorys([]);
    if (isReplyModalVisible && id) {
      getMessageReplyListRequest();
    }
  }, [isReplyModalVisible]);

  const onCancel = () => {
    setIsReplyModalVisible(false);
  };

  const handleMsgSend = () => {
    if (inputText.trim().length === 0) {
      return message.error("请输入回复内容");
    }
    const text = inputText;
    setInputText("");
    setLoading(true);
    dataApi
      .postSaveMessageReply({ messageId: id, content: text })
      .then((data) => {
        console.log("发送成功");
        getMessageReplyListRequest();
        tableReload();
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Modal
      title="留言回复"
      open={isReplyModalVisible}
      width={800}
      destroyOnClose={true}
      maskClosable={false}
      style={{ height: "80vh" }}
      onCancel={() => onCancel()}
      styles={{
        body: {
          height: "70vh",
          overflow: "auto",
        },
      }}
      footer={null}
      // footer={[
      //   <Button key="cancel" onClick={onCancel}>
      //     取消
      //   </Button>,
      //   <Button key="confirm" type="primary" htmlType="submit" form="modal">
      //     确定
      //   </Button>,
      // ]}
    >
      <Flex vertical className="reply-modal">
        <Flex flex="0 0 40px" className="top-wrapper">
          <Flex flex="0 0 60px" align="center">
            留言:
          </Flex>
          <Flex flex={1}>
            <Input.TextArea
              disabled={true}
              value={content}
              rows={3}
              placeholder="请输入留言内容"
              autoComplete="off"
              autoSize={{ minRows: 3, maxRows: 3 }}
            />
          </Flex>
        </Flex>
        <Flex flex={1} vertical className="main-wrapper" ref={containRef}>
          {historys?.length ? (
            historys?.map((history, index) => {
              return (
                <div className="main-pannel" key={index}>
                  {history.type === 0 && (
                    <div className="pannel-item user-item">
                      <div className="item-role">{history.name}</div>
                      <div className="item-container">
                        <div
                          className="item-time"
                          style={{ fontSize: "12px", marginBottom: "4px" }}
                        >
                          {dayjs(history.createTime).format(
                            "YYYY-MM-DD HH:mm:ss"
                          )}
                        </div>
                        <div className="item-content">{history.content}</div>
                      </div>
                    </div>
                  )}
                  {history.type === 1 && (
                    <div className="pannel-item admin-item">
                      {history.readFlag ? (
                        <Space size={0} style={{ marginRight: 8 }}>
                          <CheckCircleFilled
                            style={{
                              color: "#52c41a",
                              marginRight: "8px",
                              fontSize: 10,
                            }}
                          />
                          <span style={{ fontSize: 10 }}>已读</span>
                        </Space>
                      ) : (
                        <Space size={0} style={{ marginRight: 8 }}>
                          <ClockCircleFilled
                            style={{
                              color: "#faad14",
                              marginRight: "8px",
                              fontSize: 10,
                            }}
                          />
                          <span style={{ fontSize: 10 }}>未读</span>
                        </Space>
                      )}
                      <div className="item-container">
                        <div
                          className="item-time"
                          style={{ fontSize: "12px", marginBottom: "4px" }}
                        >
                          {dayjs(history.createTime).format(
                            "YYYY-MM-DD HH:mm:ss"
                          )}
                        </div>
                        <div className="item-content">{history.content}</div>
                      </div>
                      <div className="item-role">{history.name}</div>
                    </div>
                  )}
                </div>
              );
            })
          ) : (
            <div></div>
          )}
        </Flex>
        <Flex flex="0 0 50px" className="bottom-wrapper">
          <Flex flex="0 0 60px" align="center">
            回复:
          </Flex>
          <Flex flex={1} className="bottom-action">
            <Input.TextArea
              disabled={loading}
              rows={3}
              value={inputText}
              maxLength={1000}
              showCount
              onChange={(e) => {
                setInputText(e.target.value);
              }}
              placeholder="请输入回复内容"
              autoComplete="off"
              autoSize={{ minRows: 4, maxRows: 4 }}
              onPressEnter={(e) => {
                e.preventDefault();
                handleMsgSend();
              }}
            />
            <div className="action-send">
              <Button
                icon={<SendOutlined />}
                type="text"
                onClick={handleMsgSend}
              ></Button>
            </div>
          </Flex>
        </Flex>
      </Flex>
    </Modal>
  );
};

export default ReplyModal;
