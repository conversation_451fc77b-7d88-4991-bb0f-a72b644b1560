.reply-modal {
  height: 100%;
  overflow: hidden;
  .top-wrapper {
  }
  .main-wrapper {
    border: 1px solid #ccc;
    overflow: hidden;
    border-radius: 6px;
    padding: 12px;
    height: 100px;
    overflow-y: auto;
    margin: 16px 0;
    .main-pannel {
      .pannel-item {
        width: 100%;
      }
      .user-item {
        display: flex;
        justify-content: flex-start;
        margin-bottom: 16px;
        .item-container {
          max-width: 400px;
          padding: 12px 16px;
          background: rgba(0, 0, 0, 0.06);
          border-radius: 8px;
          .item-time {
            text-align: left;
            color: #888;
          }
        }
        .item-role {
          margin-right: 10px;
          text-align: center;
          border: 1px solid rgba(0, 0, 0, 0.06);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 12px;
        }
      }
      .admin-item {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 16px;
        .item-container {
          max-width: 400px;
          padding: 12px 16px;
          background: rgba(0, 0, 0, 0.06);
          border-radius: 8px;
          .item-time {
            text-align: right;
            color: #888;
          }
          .item-content {
            // text-align: right;
            display: flex;
            justify-content: flex-end;
          }
        }
        .item-role {
          margin-left: 10px;
          text-align: center;
          border: 1px solid rgba(0, 0, 0, 0.06);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 12px;
        }
      }
    }
  }
  .bottom-wrapper {
    .bottom-action {
      position: relative;
      .action-send {
        position: absolute;
        right: 10px;
        bottom: 10px;
        z-index: 10000;
      }
    }
  }
}
