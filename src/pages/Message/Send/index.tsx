import React, { useEffect, useState } from "react";
import {
  Table,
  Space,
  Popconfirm,
  message,
  But<PERSON>,
  Di<PERSON>r,
  Flex,
  type TableColumnsType,
  type TablePaginationConfig,
  DatePicker,
  Input,
  Typography,
} from "antd";
import { dataApi } from "@/apis";
import LeaveModal from "./components/LeaveModal";
import ReplyModal from "./components/ReplyModal";
import { ISendMessage } from "@/apis/data.model";
import "./index.less";
import dayjs from "dayjs";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";

const Send: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<ISendMessage[]>([]);
  const [currentRecord, setCurrentRecord] = useState<ISendMessage>();
  const [total, setTotal] = useState(0);
  const [isLeaveModalVisible, setIsLeaveModalVisible] = useState(false);
  const [isReplyModalVisible, setIsReplyModalVisible] = useState(false);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [examDate, setExamDate] = useState<any>();
  const [searchText, setSearchText] = useState("");
  const { user } = useAuth();
  const getDataSourceRequest = () => {
    setLoading(true);
    dataApi
      .postGetSendMessagePage({
        pageSize: pagination.pageSize || 20,
        pageNum: pagination.current || 1,
        // examDate: examDate,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleLeave = () => {
    setCurrentRecord(undefined);
    setIsLeaveModalVisible(true);
  };

  const handleReply = (record: ISendMessage) => {
    setCurrentRecord(record);
    setIsReplyModalVisible(true);
  };

  const handleDelete = (record: ISendMessage) => {
    dataApi.postDelMessage(record.id).then(() => {
      message.success("操作成功");
      getDataSourceRequest();
    });
  };

  const columns: TableColumnsType<ISendMessage> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "留言内容",
      dataIndex: "content",
      width: 500,
      render: (value) => {
        return (
          <Typography.Paragraph
            style={{
              margin: 0,
              padding: 0,

              cursor: "pointer",
            }}
            ellipsis={{
              rows: 3,
              symbol: "...",
              tooltip: { title: value, arrow: false },
            }}
          >
            {value}
          </Typography.Paragraph>
        );
      },
    },

    {
      title: "留言时间",
      width: 200,
      dataIndex: "createTime",
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "操作",
      fixed: "right",
      key: "action",
      width: 240,
      hidden: user?.type === 0,
      render: (_, record) => (
        <>
          <PermButton
            types={[1]}
            roles={[]}
            type="link"
            size="small"
            key="edit"
            onClick={() => handleReply(record)}
          >
            查看回复
          </PermButton>
          <Popconfirm title="确定要删除" onConfirm={() => handleDelete(record)}>
            <PermButton
              types={[1]}
              roles={[]}
              type="link"
              size="small"
              key="del"
            >
              删除
            </PermButton>
          </Popconfirm>
        </>
      ),
    },
  ];

  const handleClear = () => {
    setExamDate(undefined);
    setSearchText("");
    handleSearch();
  };

  return (
    <div className="send-page">
      <Flex vertical gap={12}>
        <Space>
          <Input
            placeholder="请输入留言内容"
            allowClear
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleClear}
          />
          {/* <DatePicker
            placeholder="发布时间"
            allowClear
            style={{ width: 200 }}
            onChange={(date) => {
              setExamDate(date);
              handleSearch();
            }}
          /> */}
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <PermButton
            types={[1]}
            roles={[]}
            type="primary"
            onClick={handleLeave}
          >
            留言
          </PermButton>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
      <LeaveModal
        id={currentRecord?.id}
        isLeaveModalVisible={isLeaveModalVisible}
        setIsLeaveModalVisible={setIsLeaveModalVisible}
        tableReload={getDataSourceRequest}
      ></LeaveModal>
      <ReplyModal
        id={currentRecord?.id}
        isReplyModalVisible={isReplyModalVisible}
        setIsReplyModalVisible={setIsReplyModalVisible}
        tableReload={getDataSourceRequest}
      ></ReplyModal>
    </div>
  );
};

export default Send;
