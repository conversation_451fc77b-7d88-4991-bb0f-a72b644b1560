import React, { useEffect, useState } from "react";
import {
  Table,
  Space,
  Popconfirm,
  message,
  But<PERSON>,
  Di<PERSON><PERSON>,
  Flex,
  type TableColumnsType,
  type TablePaginationConfig,
  DatePicker,
  Select,
  Input,
  Typography,
} from "antd";
import { dataApi } from "@/apis";

import { IReplyMessage } from "@/apis/data.model";
import "./index.less";
import dayjs from "dayjs";
import ReplyModal from "../Send/components/ReplyModal";
import PermButton from "@/components/PermButton";
import { useAuth } from "@/Auth";

const Reply: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<IReplyMessage[]>([]);
  const [currentRecord, setCurrentRecord] = useState<IReplyMessage>();
  const [total, setTotal] = useState(0);
  const [isReplyModalVisible, setIsReplyModalVisible] = useState(false);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [bizStatus, setBizStatus] = useState(undefined);
  const [searchText, setSearchText] = useState("");
  const { user } = useAuth();
  const getDataSourceRequest = () => {
    setLoading(true);
    dataApi
      .postGetMessagePage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        bizStatus: bizStatus,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        seteDataSource(data.items);
        setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleReply = (record: IReplyMessage) => {
    setCurrentRecord(record);
    setIsReplyModalVisible(true);
  };

  const handleDelete = (record: IReplyMessage) => {
    dataApi.postDelMessage(record.id).then(() => {
      message.success("操作成功");
      getDataSourceRequest();
    });
  };

  const columns: TableColumnsType<IReplyMessage> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "公司名称",
      dataIndex: "enterpriseName",
      render: (value) => value || "-",
    },
    {
      title: "留言内容",
      dataIndex: "content",
      width: 500,
      render: (value) => {
        return (
          <Typography.Paragraph
            style={{
              margin: 0,
              padding: 0,

              cursor: "pointer",
            }}
            ellipsis={{
              rows: 3,
              symbol: "...",
              tooltip: { title: value, arrow: false },
            }}
          >
            {value}
          </Typography.Paragraph>
        );
      },
    },

    {
      title: "留言时间",
      dataIndex: "createTime",
      render: (value) => {
        return value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-";
      },
    },

    {
      title: "回复状态",
      dataIndex: "bizStatus",
      width: 100,
      render: (value) => {
        switch (value) {
          case 0:
            return "待回复";
          case 1:
            return "已回复";
          default:
            return "-";
        }
      },
    },
    {
      title: "回复时间",
      dataIndex: "operatorTime",
      render: (value, record) => {
        return value && record.bizStatus === 1
          ? dayjs(value).format("YYYY-MM-DD HH:mm:ss")
          : "-";
      },
    },
    {
      title: "操作",
      key: "action",
      width: 240,
      fixed: "right",
      hidden: user?.type === 1 || (user.type === 0 && user.role === 2),
      render: (_, record) => (
        <>
          <PermButton
            types={[0]}
            roles={[1]}
            type="link"
            size="small"
            key="edit"
            onClick={() => handleReply(record)}
          >
            查看回复
          </PermButton>
          <Popconfirm title="确定要删除" onConfirm={() => handleDelete(record)}>
            <PermButton
              types={[0]}
              roles={[1]}
              type="link"
              size="small"
              key="del"
            >
              删除
            </PermButton>
          </Popconfirm>
          <Divider type="vertical" />
        </>
      ),
    },
  ];

  return (
    <div className="reply-page">
      <Flex vertical gap={12}>
        <Space>
          <Select
            placeholder="请选择回复状态"
            allowClear
            style={{ width: 200 }}
            value={bizStatus}
            onChange={(val) => {
              setBizStatus(val);
            }}
            onSelect={handleSearch}
            onClear={() => {
              setBizStatus("");
              handleSearch();
            }}
            options={[
              { value: "", label: "所有状态" },
              { value: 0, label: "待回复" },
              { value: 1, label: "已回复" },
            ]}
          />
          <Input
            placeholder="请输入企业名称"
            allowClear
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={() => {
              setSearchText("");
              handleSearch();
            }}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>

      <ReplyModal
        id={currentRecord?.id}
        isReplyModalVisible={isReplyModalVisible}
        setIsReplyModalVisible={setIsReplyModalVisible}
        tableReload={getDataSourceRequest}
      ></ReplyModal>
    </div>
  );
};

export default Reply;
