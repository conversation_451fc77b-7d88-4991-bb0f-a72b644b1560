import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Button, Input, Modal, Popconfirm, Flex, message } from "antd";
import { dataApi } from "@/apis";
import { IExamAddress } from "@/apis/data.model";
import "./index.less";

interface AddressModalProps {
  isAddressModalVisible: boolean;
  setIsAddressModalVisible: Dispatch<SetStateAction<boolean>>;
}

const AddressModal: React.FC<AddressModalProps> = (props) => {
  const { isAddressModalVisible, setIsAddressModalVisible } = props;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<Array<IExamAddress>>([]);
  const [newData, setNewData] = useState();

  useEffect(() => {
    if (isAddressModalVisible) {
      reload();
    } else {
      setDataSource([]);
    }
  }, [isAddressModalVisible]);

  const reload = () => {
    dataApi.postGetExamAddressList().then(setDataSource);
  };

  const handleNewDataChange = (e) => {
    const { value } = e.target;
    setNewData(value);
  };

  const handleSave = () => {
    setLoading(true);
    dataApi
      .postSaveExamAddress(newData)
      .then(() => {
        message.success("操作成功");
        setNewData(null);
        reload();
      })
      .finally(() => setLoading(false));
  };

  const handleDelete = (record: IExamAddress) => {
    dataApi.postDelExamAddress(record.id).then(() => {
      message.success("操作成功");
      reload();
    });
  };

  const onCancel = () => {
    setIsAddressModalVisible(false);
  };

  return (
    <Modal
      title="编辑考试地点"
      open={isAddressModalVisible}
      width={400}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          关闭
        </Button>,
      ]}
      styles={{
        body: { paddingTop: 12, paddingBottom: 12 },
        footer: { textAlign: "center" },
      }}
    >
      <Flex className="address-container" vertical gap={12}>
        <Flex className="address-list" vertical gap={12}>
          {dataSource.map((record, index) => (
            <Flex align="center" gap={12} key={index}>
              <Input value={record.name} disabled />

              <Popconfirm
                title="确定要删除"
                onConfirm={() => handleDelete(record)}
              >
                <Button type="default" size="small" danger>
                  删除
                </Button>
              </Popconfirm>
            </Flex>
          ))}
        </Flex>
        <Flex className="address-save" align="center" gap={12}>
          <Input
            placeholder="请输入考试地点"
            value={newData}
            onChange={handleNewDataChange}
          />
          <Button
            type="primary"
            size="small"
            loading={loading}
            onClick={handleSave}
          >
            保存
          </Button>
        </Flex>
      </Flex>
    </Modal>
  );
};

export default AddressModal;
