import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Table,
  Button,
  Space,
  Modal,
  Input,
  Flex,
  type TableColumnsType,
  type TablePaginationConfig,
} from "antd";
import { dataApi } from "@/apis";
import { IExamUser } from "@/apis/data.model";
import dayjs from "dayjs";

interface ReceiveViewModal {
  isReceiveViewModalVisible: boolean;
  setIsReceiveViewModalVisible: Dispatch<SetStateAction<boolean>>;
}

const ReceiveViewModal: React.FC<ReceiveViewModal> = (props) => {
  const { isReceiveViewModalVisible, setIsReceiveViewModalVisible } = props;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<Array<any>>([]);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [searchText, setSearchText] = useState("");

  useEffect(() => {
    if (isReceiveViewModalVisible) {
      getDataSourceRequest();
    }
  }, [isReceiveViewModalVisible, pagination]);

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .postGetExamUserReceiveCertificatePage({
        pageSize: pagination.pageSize || 10,
        pageNum: pagination.current || 1,
        search: searchText,
      })
      .then((data) => {
        setLoading(false);
        setDataSource(data.items);
        setTotal(Number(data.total));
      })
      .finally(() => setLoading(false));
  };

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const onCancel = () => {
    setIsReceiveViewModalVisible(false);
    setTotal(0);
    setPagination({
      current: 1,
      pageSize: 20,
    });
  };

  const columns: TableColumnsType<IExamUser> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "姓名",
      dataIndex: "name",
      minWidth: 60,
      render: (value) => value || "-",
    },
    {
      title: "身份证号码",
      dataIndex: "idCard",
      render: (value) => value || "-",
    },
    {
      title: "领取人",
      dataIndex: "recipientName",
      minWidth: 80,
      render: (value) => value || "-",
    },
    {
      title: "领取时间",
      dataIndex: "recipientTime",
      minWidth: 90,
      render: (value) =>
        value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "-",
    },
    {
      title: "领取人联系方式",
      dataIndex: "recipientPhone",
      minWidth: 132,
      render: (value) => value || "-",
    },
    {
      title: "领取方式",
      dataIndex: "receiveMethod",
      minWidth: 90,
      render: (value) => ({ 0: "现场", 1: "邮寄" })[value],
    },
    {
      title: "备注",
      dataIndex: "receiveRemark",
      minWidth: 60,
      render: (value) => value || "-",
    },
  ];

  return (
    <Modal
      title="领取查询"
      open={isReceiveViewModalVisible}
      width={980}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          关闭
        </Button>,
      ]}
    >
      <Flex vertical gap={12}>
        <Space>
          <Input
            placeholder="请输入姓名或身份证号码"
            allowClear
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onPressEnter={handleSearch}
            onClear={handleSearch}
          />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
        </Space>
        <Table
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 479px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            showSizeChanger: true,
            showTotal: (x) => `共 ${x} 条记录`,
            total,
          }}
          onChange={handleTableChange}
        />
      </Flex>
    </Modal>
  );
};

export default ReceiveViewModal;
