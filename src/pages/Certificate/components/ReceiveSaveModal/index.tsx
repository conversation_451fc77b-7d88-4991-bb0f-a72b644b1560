import React, { Dispatch, SetStateAction, useState } from "react";
import { Button, Form, Input, Modal, Select, message } from "antd";
import { dataApi } from "@/apis";

interface ReceiveSaveModalProps {
  type: 0 | 1; // 0批量 1单个
  id: string;
  isReceiveSaveModalVisible: boolean;
  setIsReceiveSaveModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const ReceiveSaveModal: React.FC<ReceiveSaveModalProps> = (props) => {
  const {
    type,
    id,
    isReceiveSaveModalVisible,
    setIsReceiveSaveModalVisible,
    tableReload,
  } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    setLoading(true);
    dataApi
      .postSaveExamUserReceiveCertificate({ ...values, type, id })
      .then(() => {
        message.success("操作成功");
        tableReload();
        onCancel();
      })
      .finally(() => setLoading(false));
  };

  const onCancel = () => {
    setIsReceiveSaveModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title="领取登记"
      open={isReceiveSaveModalVisible}
      width={400}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          保存
        </Button>,
      ]}
    >
      <Form
        preserve={false}
        form={form}
        onFinish={onFinish}
        name="modal"
        labelAlign="right"
        labelCol={{ style: { width: 120 } }}
      >
        <Form.Item
          name="recipientName"
          label="领取人"
          rules={[{ required: true }]}
        >
          <Input placeholder="请输入领取人" autoComplete="off" />
        </Form.Item>
        <Form.Item
          name="recipientPhone"
          label="领取人联系方式"
          rules={[{ required: true }]}
        >
          <Input placeholder="请输入领取人联系方式" autoComplete="off" />
        </Form.Item>
        <Form.Item
          name="receiveMethod"
          label="领取方式"
          rules={[{ required: true, message: "请选择领取方式" }]}
        >
          <Select
            placeholder="请选择领取方式"
            options={[
              { value: 0, label: "现场" },
              { value: 1, label: "邮寄" },
            ]}
          />
        </Form.Item>
        <Form.Item name="receiveRemark" label="备注">
          <Input.TextArea
            rows={2}
            placeholder="请输入备注"
            autoComplete="off"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ReceiveSaveModal;
