import React, { useEffect, useState } from "react";
import {
  Table,
  Space,
  Popconfirm,
  message,
  But<PERSON>,
  Divider,
  Flex,
  Input,
  type TableColumnsType,
  type TablePaginationConfig,
  TableProps,
} from "antd";
import { dataApi } from "@/apis";
import EditModal from "./components/EditModal";
import OpenModal from "./components/OpenModal";
import ResetModal from "./components/ResetModal";
import { ITrainingAccount } from "@/apis/data.model";
import "./index.less";
import dayjs from "dayjs";
import { useAuth } from "@/Auth";

const Staff: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, seteDataSource] = useState<ITrainingAccount[]>([]);
  const [currentRecord, setCurrentRecord] = useState<ITrainingAccount>();
  const [total, setTotal] = useState(0);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isResetModalVisible, setIsResetModalVisible] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isOpenModalVisible, setIsOpenModalVisible] = useState(false);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 20,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [searchText, setSearchText] = useState("");
  const { user } = useAuth();
  type TableRowSelection<T extends object = object> =
    TableProps<T>["rowSelection"];

  const getDataSourceRequest = () => {
    setLoading(true);

    dataApi
      .getExamTrainingAccountList({})
      .then((data) => {
        setLoading(false);
        console.log(data);
        seteDataSource(data);
        // setTotal(Number(data.total));
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(getDataSourceRequest, [pagination]);

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setPagination(pagination);
  };

  const handleSearch = () => {
    setPagination((prev) => {
      return { ...prev, current: 1 };
    });
  };

  const handleEdit = (record: ITrainingAccount) => {
    setCurrentRecord(record);
    setIsEditModalVisible(true);
  };

  const handleReset = (record: ITrainingAccount) => {
    setCurrentRecord(record);
    setIsResetModalVisible(true);
  };

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    console.log("selectedRowKeys changed: ", newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection: TableRowSelection<ITrainingAccount> = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const handleEnable = (ids: any) => {
    dataApi.editExamTrainingAccount({ type: 0, ids: ids }).then(() => {
      message.success("操作成功");
      setSelectedRowKeys([]);
      getDataSourceRequest();
    });
  };

  const handleDisable = (ids: any) => {
    dataApi.editExamTrainingAccount({ type: 1, ids: ids }).then(() => {
      message.success("操作成功");
      setSelectedRowKeys([]);
      getDataSourceRequest();
    });
  };

  const handleOpen = (ids: any[]) => {
    setSelectedIds(ids);
    setIsOpenModalVisible(true);
  };

  const handleDel = (ids: any) => {
    dataApi.editExamTrainingAccount({ type: 3, ids: ids }).then(() => {
      message.success("操作成功");
      getDataSourceRequest();
    });
  };

  const handleAdd = () => {
    setCurrentRecord(undefined);
    setIsEditModalVisible(true);
  };

  const columns: TableColumnsType<ITrainingAccount> = [
    {
      title: "序号",
      dataIndex: "index",
      width: 60,
      render: (value, record, index) => {
        return index + 1;
      },
    },
    {
      title: "账号",
      dataIndex: "account",
      minWidth: 120,
      render: (value) => value || "-",
    },
    {
      title: "姓名",
      dataIndex: "name",
      minWidth: 100,
      render: (value) => value || "-",
    },
    {
      title: "状态",
      dataIndex: "bizStatus",
      minWidth: 100,
      render: (value) => {
        return value === 0 ? "启用" : "停用";
      },
    },
    {
      title: "开始时间",
      dataIndex: "startTime",
      minWidth: 120,
      render: (value) => {
        return value ? dayjs(value).format("YYYY-MM-DD HH:mm") : "-";
      },
    },
    {
      title: "结束时间",
      dataIndex: "endTime",
      minWidth: 120,
      render: (value) => {
        return value ? dayjs(value).format("YYYY-MM-DD HH:mm") : "-";
      },
    },
    {
      title: "操作",
      key: "action",
      minWidth: 100,
      render: (_, record) => (
        <>
          {record.bizStatus === 0 && (
            <Button
              type="link"
              size="small"
              key="enable"
              onClick={() => handleDisable([record.id])}
            >
              停用
            </Button>
          )}
          {record.bizStatus === 1 && (
            <Button
              type="link"
              size="small"
              key="disable"
              onClick={() => handleEnable([record.id])}
            >
              启用
            </Button>
          )}
          <Button
            type="link"
            size="small"
            key="open"
            onClick={() => handleOpen([record.id])}
          >
            开放时间
          </Button>

          <Popconfirm
            title="确定要删除"
            onConfirm={() => handleDel([record.id])}
          >
            <Button type="link" size="small" key="del">
              删除
            </Button>
          </Popconfirm>
          <Button
            type="link"
            size="small"
            key="reset"
            onClick={() => handleReset(record)}
          >
            重置密码
          </Button>
        </>
      ),
    },
  ];

  return (
    <div className="staff">
      <Flex vertical gap={12}>
        <Space>
          <Button
            type="primary"
            disabled={!selectedRowKeys.length}
            key="enable"
            onClick={() => handleEnable(selectedRowKeys)}
          >
            启用
          </Button>
          <Button
            type="primary"
            disabled={!selectedRowKeys.length}
            key="disable"
            onClick={() => handleDisable(selectedRowKeys)}
          >
            停用
          </Button>
          <Button
            type="primary"
            disabled={!selectedRowKeys.length}
            key="open"
            onClick={() => handleOpen(selectedRowKeys)}
          >
            开放时间
          </Button>
          <Button type="primary" key="add" onClick={() => handleAdd()}>
            新增
          </Button>

          <Popconfirm
            title="确定要删除"
            onConfirm={() => handleDel(selectedRowKeys)}
          >
            <Button type="primary" disabled={!selectedRowKeys.length} key="del">
              删除
            </Button>
          </Popconfirm>
        </Space>
        <Table
          rowSelection={rowSelection}
          rowKey="id"
          columns={columns}
          tableLayout="auto"
          scroll={{ x: "max-content", y: "calc(100vh - 303px)" }}
          dataSource={dataSource}
          loading={loading}
          pagination={false}
          // onChange={handleTableChange}
        />
      </Flex>
      <EditModal
        id={currentRecord?.id}
        isEditModalVisible={isEditModalVisible}
        setIsEditModalVisible={setIsEditModalVisible}
        tableReload={getDataSourceRequest}
      ></EditModal>
      <ResetModal
        id={currentRecord?.id}
        isResetModalVisible={isResetModalVisible}
        setIsResetModalVisible={setIsResetModalVisible}
        tableReload={getDataSourceRequest}
      ></ResetModal>
      <OpenModal
        ids={selectedIds}
        isOpenModalVisible={isOpenModalVisible}
        setIsOpenModalVisible={setIsOpenModalVisible}
        dataSource={dataSource}
        tableReload={() => {
          setSelectedIds([]);
          setSelectedRowKeys([]);
          getDataSourceRequest();
        }}
      ></OpenModal>
    </div>
  );
};

export default Staff;
