import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Button, Form, Input, Modal, Select, message } from "antd";
import { dataApi } from "@/apis";

interface EditModal {
  id?: string;
  isEditModalVisible: boolean;
  setIsEditModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const EditModal: React.FC<EditModal> = (props) => {
  const { id, isEditModalVisible, setIsEditModalVisible, tableReload } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (isEditModalVisible && id) {
      dataApi.postGetSysUserId(id).then((data) => {
        form.setFieldsValue(data);
      });
    }
  }, [isEditModalVisible]);

  const onFinish = (values: any) => {
    setLoading(true);
    if (id) {
      dataApi
        .saveExamTrainingAccount(values)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .finally(() => setLoading(false));
    } else {
      dataApi
        .saveExamTrainingAccount(values)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .finally(() => setLoading(false));
    }
  };

  const onCancel = () => {
    setIsEditModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title={id ? "编辑测试账号" : "新增测试账号"}
      open={isEditModalVisible}
      width={500}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <div style={{ marginRight: 24 }}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 100 } }}
        >
          <Form.Item name="id" hidden>
            <Input></Input>
          </Form.Item>
          <Form.Item
            name="account"
            label="测试账号"
            rules={[{ required: true }]}
          >
            <Input placeholder="请输入测试及账号" autoComplete="off" />
          </Form.Item>
          <Form.Item name="name" label="姓名" rules={[{ required: true }]}>
            <Input placeholder="请输入姓名" autoComplete="off" />
          </Form.Item>
          <Form.Item name="password" label="密码" rules={[{ required: true }]}>
            <Input placeholder="请输入密码" autoComplete="off" />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default EditModal;
