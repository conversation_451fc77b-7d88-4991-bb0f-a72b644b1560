import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Button, DatePicker, Form, Input, Modal, Select, message } from "antd";
import { dataApi } from "@/apis";
import { ITrainingAccount } from "@/apis/data.model";

interface OpenModal {
  ids?: string[];
  dataSource?: ITrainingAccount[];
  isOpenModalVisible: boolean;
  setIsOpenModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const OpenModal: React.FC<OpenModal> = (props) => {
  const {
    ids,
    dataSource,
    isOpenModalVisible,
    setIsOpenModalVisible,
    tableReload,
  } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (isOpenModalVisible && ids?.length === 1) {
      const firstId = ids[0];
      const firstRecord = dataSource?.find((item) => item.id === firstId);
      form.setFieldsValue(firstRecord);
    }
  }, [isOpenModalVisible]);

  const onFinish = (values: any) => {
    console.log(values);
    setLoading(true);
    if (!ids?.length) return;
    dataApi
      .editExamTrainingAccount({
        type: 2,
        ids,
        startTime: values.startTime,
        endTime: values.endTime,
      })
      .then(() => {
        message.success("操作成功");
        tableReload();
        onCancel();
      })
      .finally(() => setLoading(false));
  };

  const onCancel = () => {
    setIsOpenModalVisible(false);
    form.resetFields();
  };

  return (
    <Modal
      title="测试账号开放时间"
      open={isOpenModalVisible}
      width={500}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <div style={{ marginRight: 24 }}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 100 } }}
        >
          <Form.Item
            name="startTime"
            label="开始时间"
            rules={[{ required: true }]}
          >
            <DatePicker
              style={{ width: 250 }}
              showTime={{ format: "HH:mm" }}
              format="YYYY-MM-DD HH:mm"
            />
          </Form.Item>
          <Form.Item
            name="endTime"
            label="结束时间"
            rules={[{ required: true }]}
          >
            <DatePicker
              style={{ width: 250 }}
              showTime={{ format: "HH:mm" }}
              format="YYYY-MM-DD HH:mm"
              disabledDate={(current) => {
                const startTime = form.getFieldValue("startTime");
                if (!startTime) return false;
                return current.isBefore(startTime, "day");
              }}
              disabledTime={(current) => {
                const startTime = form.getFieldValue("startTime");
                if (!startTime || !current)
                  return {
                    disabledHours: () => [],
                    disabledMinutes: () => [],
                    disabledSeconds: () => [],
                  };
                if (current.isSame(startTime, "day")) {
                  return {
                    disabledHours: () =>
                      Array.from({ length: startTime.hour() }, (_, i) => i),
                    disabledMinutes: (selectedHour) => {
                      if (selectedHour === startTime.hour()) {
                        return Array.from(
                          { length: startTime.minute() },
                          (_, i) => i
                        );
                      }
                      return [];
                    },
                    disabledSeconds: () => [],
                  };
                }
                return {
                  disabledHours: () => [],
                  disabledMinutes: () => [],
                  disabledSeconds: () => [],
                };
              }}
            />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default OpenModal;
