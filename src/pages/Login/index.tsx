import React, { useState } from "react";
import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { Button, Col, Flex, Form, Input, message, Row, Select } from "antd";
import { useNavigate } from "react-router-dom";
import { useForm } from "antd/lib/form/Form";
import { dataApi } from "@/apis";
import { useLocalStorageState } from "ahooks";
import { useAuth } from "@/Auth";
import LoginFlag1 from "@/assets/login_flag_1.png";
import LoginFlag2 from "@/assets/login_flag_2.png";
import LoginProject from "@/assets/login_project.png";
import LoginFooter1 from "@/assets/login_footer_1.png";
import LoginFooter2 from "@/assets/login_footer_2.png";
import LoginFooter3 from "@/assets/login_footer_3.png";
import "./index.less";
import { TOKEN_KEY } from "@/config";

const Login: React.FC = () => {
  const [form] = useForm();
  const [registerForm] = useForm();
  const navigate = useNavigate();
  const [, setToken] = useLocalStorageState(TOKEN_KEY);
  const [isLoading, setIsLoading] = useState(false);
  const [isRegister, setIsRegister] = useState(false);
  const { login, loginType } = useAuth();
  const handleLoginClick = () => {
    if (loginType === -1) {
      message.error("地址错误，请联系管理员");
      return;
    }

    form.validateFields().then((data) => {
      setIsLoading(true);
      dataApi
        .postLogin({
          // @ts-ignore
          type: loginType,
          account: data.account,
          password: data.password,
        })
        .then((data) => {
          setIsLoading(false);
          login(data);
          setToken(data);
          navigate("/home");
        })
        .catch(() => {
          setIsLoading(false);
        });
    });
  };

  const handleRegisterClick = () => {
    registerForm.validateFields().then((data) => {
      setIsLoading(true);
      dataApi
        .postRegisterEnterprise({
          serviceType: data.serviceType,
          name: data.name,
          account: data.account,
          password: data.password,
        })
        .then(() => {
          setIsLoading(false);
          message.success("注册成功");
          setIsRegister(false);
        })
        .catch(() => {
          setIsLoading(false);
        });
    });
  };

  // const initialValues = { account: "***********", password: "123456" };
  const initialValues = {};

  return (
    <div className="login">
      <div className="login-pannel-header">
        <img src={LoginProject} alt="" />
      </div>
      <div className="login-pannel-content">
        <div className="content-left">
          {loginType === 0 && <img src={LoginFlag1} alt="" />}
          {loginType === 1 && !isRegister && <img src={LoginFlag1} alt="" />}
          {loginType === 1 && isRegister && <img src={LoginFlag2} alt="" />}
        </div>
        <div className="content-right">
          <div className="login-title">
            {loginType === 0 ? "欢迎登录" : null}
            {loginType === 1 && !isRegister ? "欢迎登录" : null}
            {loginType === 1 && isRegister ? "企业账号注册" : null}
          </div>
          {/* <div className="login-subtitle">
            {loginType === 0 ? "保安考试后台管理系统" : "保安监管系统"}
          </div> */}
          <div className="login-pannel">
            {!isRegister && (
              <Form
                layout="vertical"
                form={form}
                initialValues={initialValues}
                autoComplete="off"
              >
                <Form.Item
                  name="account"
                  rules={[
                    {
                      message: loginType === 0 ? "请输入用户名" : "请输入账号",
                    },
                  ]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    size="large"
                    style={{ borderRadius: "10px" }}
                    placeholder={
                      loginType === 0 ? "请输入用户名" : "请输入账号"
                    }
                    autoComplete="off"
                  />
                </Form.Item>
                <Form.Item name="password" rules={[{ message: "请输入密码" }]}>
                  <Input.Password
                    prefix={<LockOutlined />}
                    type="password"
                    size="large"
                    style={{ borderRadius: "10px" }}
                    placeholder="请输入密码"
                    autoComplete="off"
                  />
                </Form.Item>
              </Form>
            )}
            {isRegister && (
              <Form
                layout="vertical"
                form={registerForm}
                initialValues={initialValues}
              >
                <Form.Item
                  name="serviceType"
                  rules={[{ required: true, message: "请选择保安服务类型" }]}
                >
                  <Select
                    placeholder="请选择保安服务类型"
                    size="large"
                    options={[
                      { value: "0", label: "保安培训单位" },
                      { value: "1", label: "保安服务公司（保安服务分公司）" },
                      { value: "2", label: "武装守护押运" },
                      { value: "3", label: "公司自行招用保安员的单位 " },
                      { value: "4", label: "物业" },
                      { value: "5", label: "跨区域保安服务公司" },
                    ]}
                  />
                </Form.Item>
                <Form.Item
                  name="name"
                  rules={[{ required: true, message: "请输入企业名称" }]}
                >
                  <Input
                    size="large"
                    style={{ borderRadius: "10px" }}
                    placeholder="请输入企业名称"
                    autoComplete="off"
                  />
                </Form.Item>
                <Form.Item
                  name="account"
                  rules={[{ required: true, message: "请输入企业账号" }]}
                >
                  <Input
                    size="large"
                    style={{ borderRadius: "10px" }}
                    placeholder="请输入企业账号"
                    autoComplete="off"
                  />
                </Form.Item>
                <Form.Item
                  name="password"
                  rules={[{ required: true, message: "请输入密码" }]}
                >
                  <Input
                    size="large"
                    style={{ borderRadius: "10px" }}
                    placeholder="请输入密码"
                    autoComplete="off"
                  />
                </Form.Item>
              </Form>
            )}

            <Flex vertical gap={18} style={{ paddingTop: loginType === 0 ? 20 : 10 }}>
              {!isRegister && (
                <Button
                  block
                  loading={isLoading}
                  type="primary"
                  size="large"
                  onClick={handleLoginClick}
                >
                  登录
                </Button>
              )}
              {isRegister && (
                <Button
                  block
                  loading={isLoading}
                  type="primary"
                  size="large"
                  onClick={handleRegisterClick}
                >
                  注册
                </Button>
              )}
              {loginType === 1 && !isRegister && (
                <Flex align="center" justify="center">
                  <span style={{ fontSize: 14 }}>没有账号？</span>
                  <Button
                    color="primary"
                    variant="link"
                    style={{ margin: 0, padding: 0 }}
                    onClick={() => {
                      // navigate("/register");
                      setIsRegister(true);
                    }}
                  >
                    立即注册账号
                  </Button>
                </Flex>
              )}
              {loginType === 1 && isRegister && (
                <Flex align="center" justify="center">
                  <span style={{ fontSize: 14 }}>已有账号？</span>
                  <Button
                    color="primary"
                    variant="link"
                    style={{ margin: 0, padding: 0 }}
                    onClick={() => {
                      setIsRegister(false);
                    }}
                  >
                    立即登录
                  </Button>
                </Flex>
              )}
            </Flex>
          </div>
        </div>
      </div>
      <div className="login-footer">
        {loginType === 0 && <img src={LoginFooter3} alt="" />}
        {loginType === 1 && !isRegister && <img src={LoginFooter2} alt="" />}
        {loginType === 1 && isRegister && <img src={LoginFooter1} alt="" />}
      </div>
    </div>
  );
};

export default Login;
