import React, { Dispatch, SetStateAction, useState } from "react";
import { Button, Flex, Form, message, Modal, Alert, Upload } from "antd";
import { dataApi } from "@/apis";
import { UploadOutlined } from "@ant-design/icons";
import { excelToData } from "@/utils";
import { baseUrl } from "@/config";

interface BatchAddModalProps {
  isBatchAddModalVisible: boolean;
  setIsBatchAddModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
}

const BatchAddModal: React.FC<BatchAddModalProps> = (props) => {
  const { isBatchAddModalVisible, setIsBatchAddModalVisible, tableReload } =
    props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [errorMsgs, setErrorMsgs] = useState<Array<string>>([]);

  const onFinish = (values: any) => {
    setLoading(true);
    setErrorMsgs([]);
    // 解析 Excel 数据
    excelToData(values.file.originFileObj, {
      headerRows: 2,
      header: [
        "index",
        "name",
        "sexDesc",
        "nationality",
        "idCard",
        "height",
        "educationDesc",
        "phone",
        "address",
        "registrationUnit",
        "workLocation",
        "remark",
      ],
    })
      .then(async (rows) => {
        if (!(rows?.length > 0)) {
          message.error("导入数据格式有误，请下载模板，按规范录入后重新上传！");
          setLoading(false);
          return;
        }
        // 数据处理
        rows.forEach((row) => {
          row.sex = { 男: 1, 女: 0 }[row.sexDesc];
          row.education = {
            小学以下: 0,
            小学: 1,
            初中: 2,
            中职: 3,
            高中: 4,
            大专: 5,
            本科: 6,
            研究生: 7,
          }[row.educationDesc];
        });
        // 批量保存
        await dataApi
          .postBatchSaveExamUser(rows)
          .then(() => {
            message.success("操作成功");
            tableReload();
            onCancel();
          })
          .catch((error) => {
            setErrorMsgs(error.data);
          });
      })
      .catch((error) => {
        console.error("Parse excel error", error);
        message.error("文件格式损坏，请重新选择文件上传！");
        setLoading(false);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onCancel = () => {
    setIsBatchAddModalVisible(false);
  };

  const handleUploadChange = ({ fileList }) => {
    form.setFieldsValue({
      file: fileList.length > 0 ? fileList[0] : null,
    });
  };

  return (
    <Modal
      title="批量导入考试人员数据"
      open={isBatchAddModalVisible}
      width={600}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <Flex vertical gap={12}>
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 120 } }}
        >
          <Form.Item label="文件要求">
            <Flex align="center">
              <span>请下载模板导入考试人员数据</span>
              <Button
                type="link"
                href={`${baseUrl}/api/console/file/templateDownload?type=0`}
                download="导入样表.xlsx"
              >
                下载模板
              </Button>
            </Flex>
          </Form.Item>
          <Form.Item
            name="file"
            label="选择文件"
            rules={[{ required: true, message: "请选择文件" }]}
          >
            <>
              <Upload
                maxCount={1}
                accept=".xlsx"
                beforeUpload={() => false}
                onChange={handleUploadChange}
              >
                <Button icon={<UploadOutlined />}>浏览</Button>
              </Upload>
            </>
          </Form.Item>
        </Form>
        {errorMsgs?.length > 0 && (
          <Alert
            type="error"
            message={
              <Flex vertical gap={8}>
                {errorMsgs.map((msg) => (
                  <div>{msg}</div>
                ))}
              </Flex>
            }
          />
        )}
      </Flex>
    </Modal>
  );
};

export default BatchAddModal;
