import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
  Table,
  Button,
  Form,
  Input,
  Image,
  Modal,
  Select,
  Upload,
  Flex,
  Space,
  message,
  Alert,
  type TableColumnsType,
  type UploadProps,
} from "antd";
import ImgCrop from "antd-img-crop";
import { dataApi } from "@/apis";
import { IExamUserScheduleRecordSummaryItem, IIdCard } from "@/apis/data.model";
import { fileUploadUrl, imageFallback, idCardReadUrl } from "@/config";
import { base64ToImg } from "@/utils";
import { red } from "@ant-design/colors";

interface EditModal {
  examUserId?: string;
  isEditModalVisible: boolean;
  setIsEditModalVisible: Dispatch<SetStateAction<boolean>>;
  tableReload: () => void;
  isViewMode?: boolean;
}

const EditModal: React.FC<EditModal> = (props) => {
  const {
    examUserId,
    isEditModalVisible,
    setIsEditModalVisible,
    tableReload,
    isViewMode,
  } = props;
  const [loading, setLoading] = useState(false);
  const [idCardReadLoading, setIdCardReadLoading] = useState(false);
  const [photoUrl, setPhotoUrl] = useState<string>();
  const [form] = Form.useForm();
  const [items, setItems] = useState<Array<IExamUserScheduleRecordSummaryItem>>(
    []
  );
  const [bizStatus, setBizStatus] = useState<number>();
  const [reviewContent, setReviewContent] = useState<string>();

  useEffect(() => {
    if (isEditModalVisible && examUserId) {
      if (isViewMode) {
        dataApi.postGetExamUserSummary(examUserId).then((data) => {
          form.setFieldsValue(data);
          setPhotoUrl(data.photoUrl);
          setBizStatus(data.bizStatus);
          setReviewContent(data.reviewContent);
          setItems(data.items ?? []);
        });
      } else {
        dataApi.postGetExamUser({ id: examUserId }).then((data) => {
          form.setFieldsValue(data);
          setPhotoUrl(data.photoUrl);
          setBizStatus(data.bizStatus);
          setReviewContent(data.reviewContent);
        });
      }
    }
  }, [isEditModalVisible]);

  const handleFileChange: UploadProps["onChange"] = ({ file }) => {
    if (file.status == "done") {
      form.setFieldValue("photo", file.response.data);
      setPhotoUrl(URL.createObjectURL(file.originFileObj));
    } else if (file.status == "removed") {
      form.setFieldValue("photo", null);
      setPhotoUrl(null);
    }
  };

  const handleIdCardRead = () => {
    // 只读模式不需要支持读卡
    if (isViewMode) {
      return;
    }
    setIdCardReadLoading(true);
    // 调用本地接口读卡
    fetch(idCardReadUrl)
      .then((response) => response.json())
      .then(async (idCardData: IIdCard) => {
        form.setFieldsValue({
          idCard: idCardData.cardID,
          address: idCardData.address,
          name: idCardData.name,
          nationality: idCardData.nation,
          sex: { 男: 1, 女: 0 }[idCardData.sex],
        });
        // 上传图片
        const file = base64ToImg(idCardData.photoBase64, idCardData.cardID);
        await dataApi.postFileUpload(file, 1).then((photo) => {
          form.setFieldValue("photo", photo);
          setPhotoUrl(URL.createObjectURL(file));
        });
        // 获取用户信息回填表单
        dataApi.postGetExamUser({ idCard: idCardData.cardID }).then((data) => {
          if (data) {
            form.setFieldsValue({
              id: data.id,
              height: data.height,
              education: data.education,
              phone: data.phone,
              registrationUnit: data.registrationUnit,
              workLocation: data.workLocation,
              remark: data.remark,
              registrationNo: data.registrationNo,
            });
            setBizStatus(data.bizStatus);
            setReviewContent(data.reviewContent);
          }
        });
      })
      .finally(() => {
        setIdCardReadLoading(false);
      });
  };

  const onReAudit = () => {
    Modal.confirm({
      centered: true,
      title: "提醒",
      content: `确定要重新审查吗？`,
      onOk: () =>
        dataApi.postReAuditExamUser(examUserId).then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        }),
    });
  };

  const onFinish = (values: any) => {
    setLoading(true);
    if (values.id) {
      dataApi
        .postEditExamUser(values)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .finally(() => setLoading(false));
    } else {
      dataApi
        .postSaveExamUser(values)
        .then(() => {
          message.success("操作成功");
          tableReload();
          onCancel();
        })
        .finally(() => setLoading(false));
    }
  };

  const onCancel = () => {
    setIsEditModalVisible(false);
    form.resetFields();
    setPhotoUrl(null);
    setBizStatus(null);
    setReviewContent(null);
  };

  const columns: TableColumnsType<IExamUserScheduleRecordSummaryItem> = [
    {
      title: "状态",
      dataIndex: "recordBizStatus",
      render: (value) =>
        ({ 0: "未开始", 1: "进行中", 2: "未通过", 3: "已通过", 4: "弃考" })[
          value
        ],
    },
    {
      title: "考试名称",
      dataIndex: "levelDesc",
      render: (value) => value || "-",
    },
    {
      title: "考试时间",
      dataIndex: "timeRange",
      render: (_, record) =>
        `${record.examDate} ${record.startTime}-${record.endTime}`,
    },
    {
      title: "考试地点",
      dataIndex: "examAddressName",
      render: (value) => value || "-",
    },
    {
      title: "考试类型",
      dataIndex: "examAttemptCount",
      render: (value) => `第 ${value} 次考试`,
    },
    {
      title: "是否打印准考证",
      dataIndex: "admissionPrintFlag",
      render: (value) => ({ 0: "否", 1: "是" })[value],
    },
    {
      title: "考试成绩",
      dataIndex: "score",
      render: (value) => value ?? "-",
    },
  ];

  const title = isViewMode
    ? "考试人员详情"
    : examUserId
      ? "编辑考试人员"
      : "添加考试人员";
  const subTitle = bizStatus === 2 ? `审核未通过（${reviewContent}）` : "";

  return (
    <Modal
      title={
        <Flex gap={12}>
          <div>{title}</div>
          <div style={{ color: red.primary }}>{subTitle}</div>
        </Flex>
      }
      open={isEditModalVisible}
      width={800}
      destroyOnClose={true}
      maskClosable={false}
      onCancel={() => onCancel()}
      footer={[
        !isViewMode && examUserId && bizStatus > 0 && (
          <Button key="reAudit" danger onClick={onReAudit}>
            重新审查
          </Button>
        ),
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          htmlType="submit"
          form="modal"
          loading={loading}
        >
          确定
        </Button>,
      ]}
    >
      <div
        style={{
          maxHeight: "calc(100vh - 264px)",
          overflow: "auto",
          paddingRight: 24,
        }}
      >
        <Form
          preserve={false}
          form={form}
          onFinish={onFinish}
          name="modal"
          labelAlign="right"
          labelCol={{ style: { width: 120 } }}
          disabled={isViewMode}
        >
          <Form.Item name="id" hidden>
            <Input></Input>
          </Form.Item>
          <Form.Item name="name" label="姓名" rules={[{ required: true }]}>
            <Input placeholder="请输入姓名" autoComplete="off" />
          </Form.Item>
          <Form.Item
            name="photo"
            label="照片"
            rules={[{ required: true, message: "请选择照片" }]}
          >
            <Flex align="center" gap={12}>
              <Image
                width={100}
                height={140}
                src={photoUrl}
                fallback={imageFallback}
              ></Image>
              <Flex vertical align="flex-start" gap={12}>
                <Button
                  type="primary"
                  loading={idCardReadLoading}
                  onClick={handleIdCardRead}
                >
                  身份证读取
                </Button>
                <Space>
                  {/* <ImgCrop showReset quality={1} aspect={100 / 140}> */}
                  <Upload
                    action={fileUploadUrl}
                    data={{ type: 1 }}
                    accept=".jpg,.jpeg,.png"
                    maxCount={1}
                    listType="picture"
                    showUploadList={false}
                    onChange={handleFileChange}
                  >
                    <Button type="primary">上传图片</Button>
                  </Upload>
                  {/* </ImgCrop> */}
                  <div style={{ paddingLeft: 24 }}>建议大小：295*413</div>
                </Space>
              </Flex>
            </Flex>
          </Form.Item>
          <Form.Item
            name="sex"
            label="性别"
            rules={[{ required: true, message: "请选择性别" }]}
          >
            <Select
              placeholder="请选择性别"
              allowClear
              options={[
                { value: 0, label: "女" },
                { value: 1, label: "男" },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="nationality"
            label="民族"
            rules={[{ required: true }]}
          >
            <Input placeholder="请输入民族" autoComplete="off" />
          </Form.Item>
          <Form.Item
            name="idCard"
            label="身份证号"
            rules={[{ required: true }]}
          >
            <Input placeholder="请输入身份证号" autoComplete="off" />
          </Form.Item>
          <Form.Item name="height" label="身高（厘米）">
            <Input
              placeholder={isViewMode ? "" : "请输入身高（厘米）"}
              autoComplete="off"
            />
          </Form.Item>
          <Form.Item name="education" label="学历">
            <Select
              placeholder={isViewMode ? "" : "请选择学历"}
              allowClear
              options={[
                { value: 0, label: "小学以下" },
                { value: 1, label: "小学" },
                { value: 2, label: "初中" },
                { value: 3, label: "中职" },
                { value: 4, label: "高中" },
                { value: 5, label: "大专" },
                { value: 6, label: "本科" },
                { value: 7, label: "研究生" },
              ]}
            />
          </Form.Item>
          <Form.Item name="phone" label="联系电话">
            <Input
              placeholder={isViewMode ? "" : "请输入联系电话"}
              autoComplete="off"
            />
          </Form.Item>
          <Form.Item name="address" label="家庭详细地址">
            <Input
              placeholder={isViewMode ? "" : "请输入家庭详细地址"}
              autoComplete="off"
            />
          </Form.Item>
          <Form.Item name="registrationUnit" label="报名单位">
            <Input
              placeholder={isViewMode ? "" : "请输入报名单位"}
              autoComplete="off"
            />
          </Form.Item>
          <Form.Item name="workLocation" label="工作地点">
            <Input
              placeholder={isViewMode ? "" : "请输入工作地点"}
              autoComplete="off"
            />
          </Form.Item>
          <Form.Item name="remark" label="备注">
            <Input.TextArea
              rows={2}
              placeholder={isViewMode ? "" : "请输入备注"}
              autoComplete="off"
            />
          </Form.Item>
          {!isViewMode && examUserId && (
            <Form.Item label="审查状态">
              <Input
                value={
                  { 0: "待审查", 1: "审查通过", 2: "审查未通过" }[bizStatus]
                }
                disabled
              />
            </Form.Item>
          )}
          {isViewMode && (
            <Form.Item name="registrationNo" label="报名编号">
              <Input />
            </Form.Item>
          )}
        </Form>
        {isViewMode && (
          <div style={{ paddingLeft: 24 }}>
            <Flex vertical gap={12}>
              {bizStatus === 2 && (
                <Alert
                  type="error"
                  message={subTitle}
                  style={{ color: red.primary, fontWeight: 600 }}
                ></Alert>
              )}
              <Table
                rowKey="examUserScheduleId"
                size="small"
                columns={columns}
                tableLayout="auto"
                scroll={{ x: "max-content" }}
                dataSource={items}
                pagination={false}
              />
            </Flex>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default EditModal;
