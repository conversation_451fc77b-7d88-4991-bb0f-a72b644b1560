user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    gzip  on;

    client_max_body_size 50m;

    server {
        listen 80;
        server_name **************;
        root /html/;

        location /api {
            proxy_pass http://**************:7080/api;
        }

        location / {
            index index.html;

            try_files $uri $uri/ @router;
            index index.html;
            error_page 405 =200 $request_uri;
        }

        location @router {
            rewrite ^.*$ /index.html break;

        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

    }

}